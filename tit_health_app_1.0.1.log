2025-06-21 03:47:59,837 - INFO - [MainThread] - TiT Health App 1.0.1 Starting...
2025-06-21 03:47:59,838 - INFO - [MainThread] - Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
2025-06-21 03:48:02,007 - INFO - [MainThread] - TiT Health App main initialization started...
2025-06-21 03:48:02,008 - INFO - [MainThread] - HealthCacheService initialized.
2025-06-21 03:48:02,008 - INFO - [MainThread] - HealthDataService initialized with comprehensive healthcare coverage.
2025-06-21 03:48:02,009 - INFO - [MainThread] - HealthAIService initialized with Gemini Pro.
2025-06-21 03:48:03,035 - INFO - [MainThread] - Health app UI setup complete
2025-06-21 03:48:03,036 - INFO - [MainThread] - TiT Health App 1.0.1 initialized successfully.
2025-06-21 03:48:03,568 - INFO - [Thread-1 (refresh_worker)] - Fetching health stocks data for all categories...
2025-06-21 03:48:04,177 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:48:05,582 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:48:06,651 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:48:07,342 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:48:08,050 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:48:08,642 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:48:09,197 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:48:09,809 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:48:10,845 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:48:11,452 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:48:12,097 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:48:13,290 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:48:13,891 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:48:14,505 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:48:15,145 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:48:15,790 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:48:16,369 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:48:17,021 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:48:18,295 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:48:18,929 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:48:23,703 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:48:24,246 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:48:26,036 - ERROR - [Thread-1 (refresh_worker)] - $NVTA: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
