2025-06-21 03:26:50,493 - INFO - [MainThread] - TiT Health App 1.0.1 Starting...
2025-06-21 03:26:50,496 - INFO - [MainThread] - Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
2025-06-21 03:26:52,833 - INFO - [MainThread] - TiT Health App main initialization started...
2025-06-21 03:26:52,834 - INFO - [MainThread] - HealthCacheService initialized.
2025-06-21 03:26:52,835 - INFO - [MainThread] - HealthDataService initialized with comprehensive healthcare coverage.
2025-06-21 03:26:52,835 - INFO - [MainThread] - HealthAIService initialized with Gemini Pro.
2025-06-21 03:26:54,067 - INFO - [MainThread] - Health app UI setup complete
2025-06-21 03:26:54,069 - INFO - [MainThread] - TiT Health App 1.0.1 initialized successfully.
2025-06-21 03:26:54,198 - INFO - [Thread-1 (refresh_worker)] - Fetching health stocks data for all categories...
2025-06-21 03:26:54,875 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:26:55,716 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:26:56,391 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:26:57,379 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:26:58,001 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:01,334 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:01,957 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:02,499 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:03,055 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:03,628 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:04,206 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:04,819 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:05,480 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:06,119 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:06,817 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:07,118 - INFO - [Thread-2 (refresh_worker)] - Fetching health stocks data for all categories...
2025-06-21 03:27:07,588 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:08,119 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:08,812 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:09,437 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:09,699 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:10,001 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:12,696 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:13,063 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:13,429 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:13,683 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:14,135 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:14,713 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:14,894 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:15,315 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:15,492 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:16,156 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:16,493 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:17,499 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:17,662 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:18,680 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:18,875 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:19,618 - ERROR - [Thread-1 (refresh_worker)] - $BLUE: possibly delisted; no price data found  (period=1d)
2025-06-21 03:27:20,586 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:21,628 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:21,840 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:23,482 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:23,673 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:24,179 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:25,196 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:25,207 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:25,684 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:25,702 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:26,230 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:26,687 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:26,690 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:27,205 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:27,219 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:27,735 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:27,941 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:28,309 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:28,509 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:28,832 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:29,180 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:29,391 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:29,810 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:29,815 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:30,337 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:30,352 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:30,864 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:31,070 - ERROR - [Thread-2 (refresh_worker)] - $BLUE: possibly delisted; no price data found  (period=1d)
2025-06-21 03:27:31,377 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:32,018 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:32,442 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:32,462 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:33,017 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:33,017 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:33,491 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:33,824 - ERROR - [Thread-1 (refresh_worker)] - $ONEM: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:27:34,056 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:34,058 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:34,383 - INFO - [Thread-1 (refresh_worker)] - Caching data for key: health_stocks
2025-06-21 03:27:34,384 - INFO - [Thread-1 (refresh_worker)] - Fetching health sector ETFs data...
2025-06-21 03:27:34,653 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:34,657 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:35,209 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:35,411 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:35,766 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:36,147 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:36,352 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:36,822 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:37,343 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:37,344 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:37,459 - ERROR - [Thread-3 (analysis_worker)] - Error generating health market analysis: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-06-21 03:27:37,976 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:37,977 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:38,613 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:39,150 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:39,167 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:39,622 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:39,863 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:40,276 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:40,507 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:40,766 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:40,885 - INFO - [Thread-1 (refresh_worker)] - Caching data for key: health_etfs
2025-06-21 03:27:41,268 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:41,841 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:42,359 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:42,906 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:43,401 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:43,934 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:44,311 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:44,739 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:45,190 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:45,596 - ERROR - [Thread-2 (refresh_worker)] - $ONEM: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:27:45,869 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:27:46,082 - INFO - [Thread-2 (refresh_worker)] - Caching data for key: health_stocks
2025-06-21 03:27:46,083 - INFO - [Thread-2 (refresh_worker)] - Cache hit for key: health_etfs
2025-06-21 03:28:00,903 - INFO - [MainThread] - Health application closing...
