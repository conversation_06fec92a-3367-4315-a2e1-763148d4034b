2025-06-21 04:47:16,647 - INFO - [MainThread] - TiT Stock App 1.0.1 Application Starting...
2025-06-21 04:47:16,648 - INFO - [MainThread] - Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
2025-06-21 04:47:16,649 - INFO - [MainThread] - Configuration loaded successfully.
2025-06-21 04:47:18,813 - INFO - [MainThread] - TiT Stock App main initialization started...
2025-06-21 04:47:18,814 - INFO - [MainThread] - CacheService initialized.
2025-06-21 04:47:19,270 - INFO - [MainThread] - StockDataService initialized with improved APIs and symbol management.
2025-06-21 04:47:19,271 - INFO - [MainThread] - StockAIService initialized with Gemini 1.5 Flash and working API key.
2025-06-21 04:47:19,271 - INFO - [MainThread] - Setting up UI components...
2025-06-21 04:47:20,773 - INFO - [MainThread] - UI setup complete.
2025-06-21 04:47:20,774 - INFO - [MainThread] - TiT Stock App initialized successfully.
2025-06-21 04:47:21,280 - INFO - [Thread-1 (_refresh_task)] - Fetching stock indices data with rate limiting...
2025-06-21 04:47:21,428 - ERROR - [Thread-1 (_refresh_task)] - Financial Modeling Prep API error for ^GSPC: 'calls_per_minute'
2025-06-21 04:47:23,193 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^GSPC from YFinance (Fallback)
2025-06-21 04:47:24,782 - INFO - [Thread-2 (_refresh_task)] - Fetching stock indices data with rate limiting...
2025-06-21 04:47:26,914 - INFO - [Thread-3 (_refresh_task)] - Fetching stock indices data with rate limiting...
2025-06-21 04:47:33,342 - ERROR - [Thread-1 (_refresh_task)] - Financial Modeling Prep API error for ^DJI: 'calls_per_minute'
2025-06-21 04:47:33,343 - ERROR - [Thread-1 (_refresh_task)] - Twelve Data API error for ^DJI: 'calls_per_minute'
2025-06-21 04:47:33,435 - ERROR - [Thread-3 (_refresh_task)] - Financial Modeling Prep API error for ^GSPC: 'calls_per_minute'
2025-06-21 04:47:33,436 - ERROR - [Thread-3 (_refresh_task)] - Twelve Data API error for ^GSPC: 'calls_per_minute'
2025-06-21 04:47:33,440 - ERROR - [Thread-2 (_refresh_task)] - Financial Modeling Prep API error for ^GSPC: 'calls_per_minute'
2025-06-21 04:47:33,445 - ERROR - [Thread-2 (_refresh_task)] - Twelve Data API error for ^GSPC: 'calls_per_minute'
2025-06-21 04:47:34,453 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^DJI from YFinance (Fallback)
2025-06-21 04:47:34,657 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^GSPC from YFinance (Fallback)
2025-06-21 04:47:34,678 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^GSPC from YFinance (Fallback)
2025-06-21 04:47:45,384 - ERROR - [Thread-1 (_refresh_task)] - Financial Modeling Prep API error for ^IXIC: 'calls_per_minute'
2025-06-21 04:47:45,385 - ERROR - [Thread-1 (_refresh_task)] - Twelve Data API error for ^IXIC: 'calls_per_minute'
2025-06-21 04:47:45,388 - ERROR - [Thread-2 (_refresh_task)] - Financial Modeling Prep API error for ^DJI: 'calls_per_minute'
2025-06-21 04:47:45,388 - ERROR - [Thread-3 (_refresh_task)] - Financial Modeling Prep API error for ^DJI: 'calls_per_minute'
2025-06-21 04:47:45,389 - ERROR - [Thread-2 (_refresh_task)] - Twelve Data API error for ^DJI: 'calls_per_minute'
2025-06-21 04:47:45,389 - ERROR - [Thread-3 (_refresh_task)] - Twelve Data API error for ^DJI: 'calls_per_minute'
2025-06-21 04:47:46,476 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^IXIC from YFinance (Fallback)
2025-06-21 04:47:46,528 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^DJI from YFinance (Fallback)
2025-06-21 04:47:46,580 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^DJI from YFinance (Fallback)
2025-06-21 04:47:57,343 - ERROR - [Thread-2 (_refresh_task)] - Financial Modeling Prep API error for ^IXIC: 'calls_per_minute'
2025-06-21 04:47:57,344 - ERROR - [Thread-2 (_refresh_task)] - Twelve Data API error for ^IXIC: 'calls_per_minute'
2025-06-21 04:47:57,875 - ERROR - [Thread-1 (_refresh_task)] - Financial Modeling Prep API error for ^GSPTSE: 'calls_per_minute'
2025-06-21 04:47:57,876 - ERROR - [Thread-1 (_refresh_task)] - Twelve Data API error for ^GSPTSE: 'calls_per_minute'
2025-06-21 04:47:57,877 - ERROR - [Thread-3 (_refresh_task)] - Financial Modeling Prep API error for ^IXIC: 'calls_per_minute'
2025-06-21 04:47:57,878 - ERROR - [Thread-3 (_refresh_task)] - Twelve Data API error for ^IXIC: 'calls_per_minute'
2025-06-21 04:47:58,480 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^IXIC from YFinance (Fallback)
2025-06-21 04:47:58,971 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^IXIC from YFinance (Fallback)
2025-06-21 04:47:58,990 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^GSPTSE from YFinance (Fallback)
2025-06-21 04:47:59,405 - INFO - [Thread-9 (_refresh_news_task)] - Fetching stock market news from The Globe and Mail and other sources...
2025-06-21 04:47:59,406 - INFO - [Thread-9 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/business/
2025-06-21 04:47:59,651 - INFO - [Thread-10 (_refresh_news_task)] - Fetching stock market news from The Globe and Mail and other sources...
2025-06-21 04:47:59,652 - INFO - [Thread-10 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/business/
2025-06-21 04:48:00,227 - INFO - [Thread-9 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/investing/
2025-06-21 04:48:00,739 - INFO - [Thread-10 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/investing/
2025-06-21 04:48:01,193 - INFO - [Thread-9 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/section/report-on-business/
2025-06-21 04:48:01,639 - INFO - [Thread-10 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/section/report-on-business/
2025-06-21 04:48:01,935 - INFO - [Thread-9 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/economy/
2025-06-21 04:48:02,256 - INFO - [Thread-10 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/economy/
2025-06-21 04:48:02,628 - INFO - [Thread-10 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/section/globe-investor/
2025-06-21 04:48:02,945 - INFO - [Thread-9 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/section/globe-investor/
2025-06-21 04:48:03,419 - INFO - [Thread-10 (_refresh_news_task)] - Parsing RSS feed: https://feeds.reuters.com/reuters/businessNews
2025-06-21 04:48:03,759 - INFO - [Thread-9 (_refresh_news_task)] - Parsing RSS feed: https://feeds.reuters.com/reuters/businessNews
2025-06-21 04:48:03,818 - INFO - [Thread-10 (_refresh_news_task)] - Parsing RSS feed: https://feeds.reuters.com/news/economy
2025-06-21 04:48:04,208 - INFO - [Thread-9 (_refresh_news_task)] - Parsing RSS feed: https://feeds.reuters.com/news/economy
2025-06-21 04:48:04,514 - INFO - [Thread-10 (_refresh_news_task)] - Parsing RSS feed: https://www.cnbc.com/id/100003114/device/rss/rss.html
2025-06-21 04:48:04,932 - INFO - [Thread-9 (_refresh_news_task)] - Parsing RSS feed: https://www.cnbc.com/id/100003114/device/rss/rss.html
2025-06-21 04:48:05,910 - INFO - [Thread-9 (_refresh_news_task)] - Parsing RSS feed: https://www.cnbc.com/id/10000664/device/rss/rss.html
2025-06-21 04:48:06,044 - INFO - [Thread-10 (_refresh_news_task)] - Parsing RSS feed: https://www.cnbc.com/id/10000664/device/rss/rss.html
2025-06-21 04:48:06,972 - INFO - [Thread-9 (_refresh_news_task)] - Parsing RSS feed: https://feeds.bloomberg.com/markets/news.rss
2025-06-21 04:48:06,988 - INFO - [Thread-10 (_refresh_news_task)] - Parsing RSS feed: https://feeds.bloomberg.com/markets/news.rss
2025-06-21 04:48:08,196 - INFO - [Thread-10 (_refresh_news_task)] - Parsing RSS feed: https://feeds.finance.yahoo.com/rss/2.0/headline
2025-06-21 04:48:08,548 - INFO - [Thread-9 (_refresh_news_task)] - Parsing RSS feed: https://feeds.finance.yahoo.com/rss/2.0/headline
2025-06-21 04:48:09,065 - INFO - [Thread-10 (_refresh_news_task)] - Parsing RSS feed: https://www.marketwatch.com/rss/topstories
2025-06-21 04:48:09,373 - INFO - [Thread-9 (_refresh_news_task)] - Parsing RSS feed: https://www.marketwatch.com/rss/topstories
2025-06-21 04:48:09,585 - ERROR - [Thread-1 (_refresh_task)] - Financial Modeling Prep API error for 000001.SS: 'calls_per_minute'
2025-06-21 04:48:09,586 - ERROR - [Thread-3 (_refresh_task)] - Financial Modeling Prep API error for ^GSPTSE: 'calls_per_minute'
2025-06-21 04:48:09,587 - ERROR - [Thread-2 (_refresh_task)] - Financial Modeling Prep API error for ^GSPTSE: 'calls_per_minute'
2025-06-21 04:48:09,587 - ERROR - [Thread-1 (_refresh_task)] - Twelve Data API error for 000001.SS: 'calls_per_minute'
2025-06-21 04:48:09,588 - ERROR - [Thread-3 (_refresh_task)] - Twelve Data API error for ^GSPTSE: 'calls_per_minute'
2025-06-21 04:48:09,589 - ERROR - [Thread-2 (_refresh_task)] - Twelve Data API error for ^GSPTSE: 'calls_per_minute'
2025-06-21 04:48:09,590 - WARNING - [Thread-1 (_refresh_task)] - YFinance rate limit reached, skipping 000001.SS
2025-06-21 04:48:09,591 - WARNING - [Thread-3 (_refresh_task)] - YFinance rate limit reached, skipping ^GSPTSE
2025-06-21 04:48:09,591 - WARNING - [Thread-2 (_refresh_task)] - YFinance rate limit reached, skipping ^GSPTSE
2025-06-21 04:48:09,591 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for 000001.SS: No data available from any API source
2025-06-21 04:48:09,592 - WARNING - [Thread-3 (_refresh_task)] - Creating placeholder data for ^GSPTSE: No data available from any API source
2025-06-21 04:48:09,594 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for ^GSPTSE: No data available from any API source
2025-06-21 04:48:10,449 - INFO - [Thread-9 (_refresh_news_task)] - Parsing RSS feed: https://www.ft.com/rss/home/<USER>
2025-06-21 04:48:10,585 - INFO - [Thread-10 (_refresh_news_task)] - Parsing RSS feed: https://www.ft.com/rss/home/<USER>
2025-06-21 04:48:11,326 - INFO - [Thread-10 (_refresh_news_task)] - Parsing RSS feed: https://www.ft.com/rss/home/<USER>
2025-06-21 04:48:11,530 - INFO - [Thread-9 (_refresh_news_task)] - Parsing RSS feed: https://www.ft.com/rss/home/<USER>
2025-06-21 04:48:12,146 - INFO - [Thread-10 (_refresh_news_task)] - Parsing RSS feed: https://www.ft.com/rss/home/<USER>
2025-06-21 04:48:12,368 - INFO - [Thread-9 (_refresh_news_task)] - Parsing RSS feed: https://www.ft.com/rss/home/<USER>
2025-06-21 04:48:12,906 - INFO - [Thread-9 (_refresh_news_task)] - Successfully fetched 42 stock market news articles
2025-06-21 04:48:12,907 - INFO - [Thread-10 (_refresh_news_task)] - Successfully fetched 42 stock market news articles
2025-06-21 04:48:12,908 - INFO - [Thread-9 (_refresh_news_task)] - The Globe and Mail articles: 30, Other sources: 12
2025-06-21 04:48:12,908 - INFO - [Thread-10 (_refresh_news_task)] - The Globe and Mail articles: 30, Other sources: 12
2025-06-21 04:48:12,909 - INFO - [Thread-9 (_refresh_news_task)] - Caching data for key: news
2025-06-21 04:48:12,909 - INFO - [Thread-10 (_refresh_news_task)] - Caching data for key: news
2025-06-21 04:48:21,430 - ERROR - [Thread-1 (_refresh_task)] - Financial Modeling Prep API error for 399001.SZ: 'calls_per_minute'
2025-06-21 04:48:21,431 - ERROR - [Thread-2 (_refresh_task)] - Financial Modeling Prep API error for 000001.SS: 'calls_per_minute'
2025-06-21 04:48:21,431 - ERROR - [Thread-1 (_refresh_task)] - Twelve Data API error for 399001.SZ: 'calls_per_minute'
2025-06-21 04:48:21,432 - ERROR - [Thread-3 (_refresh_task)] - Financial Modeling Prep API error for 000001.SS: 'calls_per_minute'
2025-06-21 04:48:21,433 - ERROR - [Thread-2 (_refresh_task)] - Twelve Data API error for 000001.SS: 'calls_per_minute'
2025-06-21 04:48:21,434 - WARNING - [Thread-1 (_refresh_task)] - YFinance rate limit reached, skipping 399001.SZ
2025-06-21 04:48:21,434 - ERROR - [Thread-3 (_refresh_task)] - Twelve Data API error for 000001.SS: 'calls_per_minute'
2025-06-21 04:48:21,434 - WARNING - [Thread-2 (_refresh_task)] - YFinance rate limit reached, skipping 000001.SS
2025-06-21 04:48:21,435 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for 399001.SZ: No data available from any API source
2025-06-21 04:48:21,435 - WARNING - [Thread-3 (_refresh_task)] - YFinance rate limit reached, skipping 000001.SS
2025-06-21 04:48:21,437 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for 000001.SS: No data available from any API source
2025-06-21 04:48:21,439 - WARNING - [Thread-3 (_refresh_task)] - Creating placeholder data for 000001.SS: No data available from any API source
2025-06-21 04:48:33,456 - ERROR - [Thread-2 (_refresh_task)] - Financial Modeling Prep API error for 399001.SZ: 'calls_per_minute'
2025-06-21 04:48:33,458 - ERROR - [Thread-2 (_refresh_task)] - Twelve Data API error for 399001.SZ: 'calls_per_minute'
2025-06-21 04:48:33,459 - ERROR - [Thread-3 (_refresh_task)] - Financial Modeling Prep API error for 399001.SZ: 'calls_per_minute'
2025-06-21 04:48:33,459 - WARNING - [Thread-2 (_refresh_task)] - YFinance rate limit reached, skipping 399001.SZ
2025-06-21 04:48:33,459 - ERROR - [Thread-1 (_refresh_task)] - Financial Modeling Prep API error for ^IMOEX.ME: 'calls_per_minute'
2025-06-21 04:48:33,460 - ERROR - [Thread-3 (_refresh_task)] - Twelve Data API error for 399001.SZ: 'calls_per_minute'
2025-06-21 04:48:33,461 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for 399001.SZ: No data available from any API source
2025-06-21 04:48:33,461 - ERROR - [Thread-1 (_refresh_task)] - Twelve Data API error for ^IMOEX.ME: 'calls_per_minute'
2025-06-21 04:48:33,462 - WARNING - [Thread-3 (_refresh_task)] - YFinance rate limit reached, skipping 399001.SZ
2025-06-21 04:48:33,464 - WARNING - [Thread-1 (_refresh_task)] - YFinance rate limit reached, skipping ^IMOEX.ME
2025-06-21 04:48:33,464 - WARNING - [Thread-3 (_refresh_task)] - Creating placeholder data for 399001.SZ: No data available from any API source
2025-06-21 04:48:33,466 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^IMOEX.ME: No data available from any API source
2025-06-21 04:48:45,430 - ERROR - [Thread-2 (_refresh_task)] - Financial Modeling Prep API error for ^IMOEX.ME: 'calls_per_minute'
2025-06-21 04:48:45,430 - ERROR - [Thread-1 (_refresh_task)] - Twelve Data API error for VCB.VN: 'calls_per_minute'
2025-06-21 04:48:45,431 - ERROR - [Thread-2 (_refresh_task)] - Twelve Data API error for ^IMOEX.ME: 'calls_per_minute'
2025-06-21 04:48:45,431 - ERROR - [Thread-3 (_refresh_task)] - Financial Modeling Prep API error for ^IMOEX.ME: 'calls_per_minute'
2025-06-21 04:48:45,432 - ERROR - [Thread-1 (_refresh_task)] - Financial Modeling Prep API error for VCB.VN: 'calls_per_minute'
2025-06-21 04:48:45,433 - WARNING - [Thread-2 (_refresh_task)] - YFinance rate limit reached, skipping ^IMOEX.ME
2025-06-21 04:48:45,434 - ERROR - [Thread-3 (_refresh_task)] - Twelve Data API error for ^IMOEX.ME: 'calls_per_minute'
2025-06-21 04:48:45,435 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for ^IMOEX.ME: No data available from any API source
2025-06-21 04:48:45,435 - WARNING - [Thread-3 (_refresh_task)] - YFinance rate limit reached, skipping ^IMOEX.ME
2025-06-21 04:48:45,436 - WARNING - [Thread-3 (_refresh_task)] - Creating placeholder data for ^IMOEX.ME: No data available from any API source
2025-06-21 04:48:57,432 - ERROR - [Thread-3 (_refresh_task)] - Twelve Data API error for VCB.VN: 'calls_per_minute'
2025-06-21 04:48:57,432 - ERROR - [Thread-2 (_refresh_task)] - Twelve Data API error for VCB.VN: 'calls_per_minute'
2025-06-21 04:48:57,433 - ERROR - [Thread-1 (_refresh_task)] - Financial Modeling Prep API error for VCB.VN: 'calls_per_minute'
2025-06-21 04:48:57,433 - ERROR - [Thread-3 (_refresh_task)] - Financial Modeling Prep API error for VCB.VN: 'calls_per_minute'
2025-06-21 04:48:57,434 - ERROR - [Thread-2 (_refresh_task)] - Financial Modeling Prep API error for VCB.VN: 'calls_per_minute'
2025-06-21 04:48:57,434 - ERROR - [Thread-1 (_refresh_task)] - Twelve Data API error for VCB.VN: 'calls_per_minute'
2025-06-21 04:48:57,435 - WARNING - [Thread-1 (_refresh_task)] - YFinance rate limit reached, skipping VCB.VN
2025-06-21 04:48:57,435 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^VNI: No data available from any API source
2025-06-21 04:49:09,452 - ERROR - [Thread-2 (_refresh_task)] - Financial Modeling Prep API error for VCB.VN: 'calls_per_minute'
2025-06-21 04:49:09,453 - ERROR - [Thread-1 (_refresh_task)] - Twelve Data API error for VIC.VN: 'calls_per_minute'
2025-06-21 04:49:09,454 - ERROR - [Thread-3 (_refresh_task)] - Financial Modeling Prep API error for VCB.VN: 'calls_per_minute'
2025-06-21 04:49:09,454 - ERROR - [Thread-2 (_refresh_task)] - Twelve Data API error for VCB.VN: 'calls_per_minute'
2025-06-21 04:49:09,454 - ERROR - [Thread-1 (_refresh_task)] - Financial Modeling Prep API error for VIC.VN: 'calls_per_minute'
2025-06-21 04:49:09,455 - ERROR - [Thread-3 (_refresh_task)] - Twelve Data API error for VCB.VN: 'calls_per_minute'
2025-06-21 04:49:09,456 - WARNING - [Thread-2 (_refresh_task)] - YFinance rate limit reached, skipping VCB.VN
2025-06-21 04:49:09,457 - WARNING - [Thread-3 (_refresh_task)] - YFinance rate limit reached, skipping VCB.VN
2025-06-21 04:49:09,457 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for ^VNI: No data available from any API source
2025-06-21 04:49:09,458 - WARNING - [Thread-3 (_refresh_task)] - Creating placeholder data for ^VNI: No data available from any API source
2025-06-21 04:49:21,437 - ERROR - [Thread-2 (_refresh_task)] - Twelve Data API error for VIC.VN: 'calls_per_minute'
2025-06-21 04:49:21,437 - ERROR - [Thread-2 (_refresh_task)] - Financial Modeling Prep API error for VIC.VN: 'calls_per_minute'
2025-06-21 04:49:21,441 - ERROR - [Thread-3 (_refresh_task)] - Twelve Data API error for VIC.VN: 'calls_per_minute'
2025-06-21 04:49:21,442 - ERROR - [Thread-1 (_refresh_task)] - Financial Modeling Prep API error for VIC.VN: 'calls_per_minute'
2025-06-21 04:49:21,442 - ERROR - [Thread-3 (_refresh_task)] - Financial Modeling Prep API error for VIC.VN: 'calls_per_minute'
2025-06-21 04:49:21,443 - ERROR - [Thread-1 (_refresh_task)] - Twelve Data API error for VIC.VN: 'calls_per_minute'
2025-06-21 04:49:21,444 - WARNING - [Thread-1 (_refresh_task)] - YFinance rate limit reached, skipping VIC.VN
2025-06-21 04:49:21,445 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^VN30: No data available from any API source
2025-06-21 04:49:33,449 - ERROR - [Thread-2 (_refresh_task)] - Financial Modeling Prep API error for VIC.VN: 'calls_per_minute'
2025-06-21 04:49:33,450 - ERROR - [Thread-2 (_refresh_task)] - Twelve Data API error for VIC.VN: 'calls_per_minute'
2025-06-21 04:49:33,451 - WARNING - [Thread-2 (_refresh_task)] - YFinance rate limit reached, skipping VIC.VN
2025-06-21 04:49:33,453 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for ^VN30: No data available from any API source
2025-06-21 04:49:33,453 - ERROR - [Thread-1 (_refresh_task)] - Financial Modeling Prep API error for ^GDAXI: 'calls_per_minute'
2025-06-21 04:49:33,454 - ERROR - [Thread-1 (_refresh_task)] - Twelve Data API error for ^GDAXI: 'calls_per_minute'
2025-06-21 04:49:33,455 - WARNING - [Thread-1 (_refresh_task)] - YFinance rate limit reached, skipping ^GDAXI
2025-06-21 04:49:33,455 - ERROR - [Thread-3 (_refresh_task)] - Financial Modeling Prep API error for VIC.VN: 'calls_per_minute'
2025-06-21 04:49:33,456 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^GDAXI: No data available from any API source
2025-06-21 04:49:33,457 - ERROR - [Thread-3 (_refresh_task)] - Twelve Data API error for VIC.VN: 'calls_per_minute'
2025-06-21 04:49:33,460 - WARNING - [Thread-3 (_refresh_task)] - YFinance rate limit reached, skipping VIC.VN
2025-06-21 04:49:33,461 - WARNING - [Thread-3 (_refresh_task)] - Creating placeholder data for ^VN30: No data available from any API source
