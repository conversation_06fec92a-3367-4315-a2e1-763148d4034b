2025-06-21 04:17:42,538 - INFO - [MainThread] - TiT Stock App 1.0.1 Application Starting...
2025-06-21 04:17:42,539 - INFO - [MainThread] - Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
2025-06-21 04:17:42,539 - INFO - [MainThread] - Configuration loaded successfully.
2025-06-21 04:17:44,424 - INFO - [MainThread] - TiT Stock App main initialization started...
2025-06-21 04:17:44,424 - INFO - [MainThread] - CacheService initialized.
2025-06-21 04:17:44,425 - INFO - [MainThread] - StockDataService initialized.
2025-06-21 04:17:44,425 - INFO - [MainThread] - StockAIService initialized with Gemini 1.5 Flash and working API key.
2025-06-21 04:17:44,426 - INFO - [MainThread] - Setting up UI components...
2025-06-21 04:17:45,673 - CRITICAL - [MainThread] - A critical, unhandled error occurred: 'TiTStockApp' object has no attribute 'commodities_tree'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Stock_App_1.0.1.py", line 2998, in <module>
    app = TiTStockApp(root)
          ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Stock_App_1.0.1.py", line 1312, in __init__
    self.setup_ui()
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Stock_App_1.0.1.py", line 1382, in setup_ui
    self.create_search_tab()
  File "C:\Users\<USER>\Desktop\The Teu Project\file Teu ontrack\Teu 1.0\TiT_Suite_Complete_Package\TiT_Stock_App_1.0.1.py", line 1606, in create_search_tab
    self.commodities_tree.bind('<Double-1>', self.show_commodity_details)
    ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'TiTStockApp' object has no attribute 'commodities_tree'
