2025-06-21 03:24:26,856 - INFO - [MainThread] - TiT Stock App 1.0.1 Application Starting...
2025-06-21 03:24:26,857 - INFO - [MainThread] - Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
2025-06-21 03:24:26,858 - INFO - [MainThread] - Configuration loaded successfully.
2025-06-21 03:24:29,039 - INFO - [MainThread] - TiT Stock App main initialization started...
2025-06-21 03:24:29,039 - INFO - [MainThread] - CacheService initialized.
2025-06-21 03:24:29,040 - INFO - [MainThread] - StockDataService initialized.
2025-06-21 03:24:29,040 - INFO - [MainThread] - StockAIService initialized with Gemini 1.5 Flash and working API key.
2025-06-21 03:24:29,041 - INFO - [MainThread] - Setting up UI components...
2025-06-21 03:24:30,726 - INFO - [MainThread] - UI setup complete.
2025-06-21 03:24:30,729 - INFO - [MainThread] - Ti<PERSON> <PERSON> App initialized successfully.
2025-06-21 03:24:31,320 - INFO - [Thread-1 (_refresh_task)] - Fetching stock indices data with rate limiting...
2025-06-21 03:24:33,233 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^GSPC
2025-06-21 03:24:33,854 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^DJI
2025-06-21 03:24:34,518 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^IXIC
2025-06-21 03:24:35,123 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^GSPTSE
2025-06-21 03:24:35,613 - INFO - [Thread-2 (_refresh_task)] - Fetching stock indices data with rate limiting...
2025-06-21 03:24:35,757 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for 000001.SS
2025-06-21 03:24:36,276 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^GSPC
2025-06-21 03:24:36,362 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for 399001.SZ
2025-06-21 03:24:36,863 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^DJI
2025-06-21 03:24:37,412 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 404: 
2025-06-21 03:24:37,503 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^IXIC
2025-06-21 03:24:37,882 - ERROR - [Thread-1 (_refresh_task)] - $^IMOEX.ME: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:24:37,884 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^IMOEX.ME: Empty or insufficient historical data
2025-06-21 03:24:38,083 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^GSPTSE
2025-06-21 03:24:38,510 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^VNI
2025-06-21 03:24:38,668 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for 000001.SS
2025-06-21 03:24:39,167 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^VN30
2025-06-21 03:24:39,254 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for 399001.SZ
2025-06-21 03:24:39,751 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^GDAXI
2025-06-21 03:24:40,074 - ERROR - [Thread-2 (_refresh_task)] - HTTP Error 404: 
2025-06-21 03:24:40,376 - ERROR - [Thread-2 (_refresh_task)] - $^IMOEX.ME: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:24:40,377 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for ^IMOEX.ME: Empty or insufficient historical data
2025-06-21 03:24:40,499 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^N225
2025-06-21 03:24:40,977 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^VNI
2025-06-21 03:24:41,087 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^FTSE
2025-06-21 03:24:41,570 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^VN30
2025-06-21 03:24:41,682 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^FCHI
2025-06-21 03:24:42,274 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^GDAXI
2025-06-21 03:24:42,682 - ERROR - [Thread-1 (_refresh_task)] - $^FTMIB: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:24:42,683 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^FTMIB: Empty or insufficient historical data
2025-06-21 03:24:42,857 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^N225
2025-06-21 03:24:43,338 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^KS11
2025-06-21 03:24:43,464 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^FTSE
2025-06-21 03:24:43,919 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^BSESN
2025-06-21 03:24:44,054 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^FCHI
2025-06-21 03:24:44,560 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^NSEI
2025-06-21 03:24:44,776 - ERROR - [Thread-2 (_refresh_task)] - $^FTMIB: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:24:44,777 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for ^FTMIB: Empty or insufficient historical data
2025-06-21 03:24:45,168 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^BVSP
2025-06-21 03:24:45,364 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^KS11
2025-06-21 03:24:45,754 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^MXX
2025-06-21 03:24:45,968 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^BSESN
2025-06-21 03:24:46,390 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^AXJO
2025-06-21 03:24:46,565 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^NSEI
2025-06-21 03:24:46,982 - ERROR - [Thread-1 (_refresh_task)] - $^XU100: possibly delisted; no price data found  (period=2d)
2025-06-21 03:24:46,983 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^XU100: Empty or insufficient historical data
2025-06-21 03:24:47,231 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^BVSP
2025-06-21 03:24:47,852 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^MXX
2025-06-21 03:24:48,176 - ERROR - [Thread-1 (_refresh_task)] - $^TASI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:24:48,178 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^TASI: Empty or insufficient historical data
2025-06-21 03:24:48,432 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^AXJO
2025-06-21 03:24:49,049 - ERROR - [Thread-2 (_refresh_task)] - $^XU100: possibly delisted; no price data found  (period=2d)
2025-06-21 03:24:49,050 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for ^XU100: Empty or insufficient historical data
2025-06-21 03:24:49,307 - ERROR - [Thread-1 (_refresh_task)] - $^ADI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:24:49,308 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^ADI: Empty or insufficient historical data
2025-06-21 03:24:49,774 - ERROR - [Thread-2 (_refresh_task)] - $^TASI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:24:49,776 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for ^TASI: Empty or insufficient historical data
2025-06-21 03:24:49,937 - ERROR - [Thread-1 (_refresh_task)] - $^QSI: possibly delisted; no price data found  (period=2d)
2025-06-21 03:24:49,939 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^QSI: Empty or insufficient historical data
2025-06-21 03:24:50,596 - ERROR - [Thread-2 (_refresh_task)] - $^ADI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:24:50,598 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for ^ADI: Empty or insufficient historical data
2025-06-21 03:24:51,108 - ERROR - [Thread-1 (_refresh_task)] - $^KWTI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:24:51,110 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^KWTI: Empty or insufficient historical data
2025-06-21 03:24:51,110 - INFO - [Thread-1 (_refresh_task)] - Caching data for INSTANT access: indices
2025-06-21 03:24:51,110 - INFO - [Thread-1 (_refresh_task)] - Caching data for key: indices_data
2025-06-21 03:24:51,232 - ERROR - [Thread-2 (_refresh_task)] - $^QSI: possibly delisted; no price data found  (period=2d)
2025-06-21 03:24:51,233 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for ^QSI: Empty or insufficient historical data
2025-06-21 03:24:52,069 - ERROR - [Thread-2 (_refresh_task)] - $^KWTI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:24:52,072 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for ^KWTI: Empty or insufficient historical data
2025-06-21 03:24:52,073 - INFO - [Thread-2 (_refresh_task)] - Caching data for INSTANT access: indices
2025-06-21 03:24:52,074 - INFO - [Thread-2 (_refresh_task)] - Caching data for key: indices_data
2025-06-21 03:24:53,803 - INFO - [Thread-1 (_refresh_task)] - Fetching commodities data...
2025-06-21 03:24:53,806 - INFO - [Thread-2 (_refresh_task)] - Fetching commodities data...
2025-06-21 03:24:54,040 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:24:54,057 - ERROR - [Thread-2 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:24:54,596 - ERROR - [Thread-2 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:24:54,598 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:24:55,132 - ERROR - [Thread-2 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:24:55,140 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:24:55,648 - ERROR - [Thread-2 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:24:55,650 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:24:56,241 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:24:56,243 - ERROR - [Thread-2 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:24:56,751 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:24:56,763 - ERROR - [Thread-2 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:24:57,280 - ERROR - [Thread-2 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:24:57,286 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:24:57,846 - ERROR - [Thread-2 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:24:57,848 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:24:58,097 - INFO - [Thread-2 (_refresh_task)] - Caching data for key: commodities_data
2025-06-21 03:24:58,098 - ERROR - [Thread-2 (_refresh_task)] - Error refreshing data: 'TiTStockApp' object has no attribute 'update_commodities_display'
2025-06-21 03:24:58,136 - INFO - [Thread-1 (_refresh_task)] - Caching data for key: commodities_data
2025-06-21 03:24:58,137 - ERROR - [Thread-1 (_refresh_task)] - Error refreshing data: 'TiTStockApp' object has no attribute 'update_commodities_display'
2025-06-21 03:24:58,243 - INFO - [Thread-3 (_refresh_task)] - Cache hit for key: indices_data
2025-06-21 03:24:58,244 - INFO - [Thread-3 (_refresh_task)] - Using cached indices data to avoid HTTP 401 errors
2025-06-21 03:24:58,255 - INFO - [Thread-3 (_refresh_task)] - Cache hit for key: commodities_data
2025-06-21 03:24:58,257 - ERROR - [Thread-3 (_refresh_task)] - Error refreshing data: 'TiTStockApp' object has no attribute 'update_commodities_display'
