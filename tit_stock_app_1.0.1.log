2025-06-21 03:35:52,650 - INFO - [MainThread] - TiT Stock App 1.0.1 Application Starting...
2025-06-21 03:35:52,651 - INFO - [MainThread] - Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
2025-06-21 03:35:52,651 - INFO - [MainThread] - Configuration loaded successfully.
2025-06-21 03:35:54,793 - INFO - [MainThread] - TiT Stock App main initialization started...
2025-06-21 03:35:54,794 - INFO - [MainThread] - CacheService initialized.
2025-06-21 03:35:54,796 - INFO - [MainThread] - StockDataService initialized.
2025-06-21 03:35:54,798 - INFO - [MainThread] - StockAIService initialized with Gemini 1.5 Flash and working API key.
2025-06-21 03:35:54,800 - INFO - [MainThread] - Setting up UI components...
2025-06-21 03:35:56,501 - INFO - [MainThread] - UI setup complete.
2025-06-21 03:35:56,502 - INFO - [MainThread] - Ti<PERSON> Stock App initialized successfully.
2025-06-21 03:35:56,622 - INFO - [Thread-1 (_refresh_task)] - Fetching stock indices data with rate limiting...
2025-06-21 03:35:57,655 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^GSPC
2025-06-21 03:35:58,252 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^DJI
2025-06-21 03:35:58,899 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^IXIC
2025-06-21 03:35:59,560 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^GSPTSE
2025-06-21 03:36:00,152 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for 000001.SS
2025-06-21 03:36:00,744 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for 399001.SZ
2025-06-21 03:36:01,144 - INFO - [Thread-2 (_refresh_task)] - Fetching stock indices data with rate limiting...
2025-06-21 03:36:02,072 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:02,213 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^GSPC
2025-06-21 03:36:02,569 - ERROR - [Thread-1 (_refresh_task)] - $^IMOEX.ME: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:36:02,570 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^IMOEX.ME: Empty or insufficient historical data
2025-06-21 03:36:02,848 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^DJI
2025-06-21 03:36:03,158 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^VNI
2025-06-21 03:36:03,477 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^IXIC
2025-06-21 03:36:03,507 - INFO - [Thread-3 (_refresh_task)] - Fetching stock indices data with rate limiting...
2025-06-21 03:36:03,739 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^VN30
2025-06-21 03:36:04,142 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^GSPTSE
2025-06-21 03:36:04,211 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^GSPC
2025-06-21 03:36:04,373 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^GDAXI
2025-06-21 03:36:04,833 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for 000001.SS
2025-06-21 03:36:04,919 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^DJI
2025-06-21 03:36:04,988 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^N225
2025-06-21 03:36:05,460 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for 399001.SZ
2025-06-21 03:36:05,554 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^IXIC
2025-06-21 03:36:05,634 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^FTSE
2025-06-21 03:36:06,213 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^GSPTSE
2025-06-21 03:36:06,316 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^FCHI
2025-06-21 03:36:06,359 - ERROR - [Thread-2 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:06,704 - ERROR - [Thread-2 (_refresh_task)] - $^IMOEX.ME: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:36:06,705 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for ^IMOEX.ME: Empty or insufficient historical data
2025-06-21 03:36:06,818 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for 000001.SS
2025-06-21 03:36:07,287 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^VNI
2025-06-21 03:36:07,473 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for 399001.SZ
2025-06-21 03:36:07,474 - ERROR - [Thread-1 (_refresh_task)] - $^FTMIB: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:36:07,475 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^FTMIB: Empty or insufficient historical data
2025-06-21 03:36:07,867 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^VN30
2025-06-21 03:36:08,078 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^KS11
2025-06-21 03:36:08,363 - ERROR - [Thread-3 (_refresh_task)] - $^IMOEX.ME: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:36:08,364 - WARNING - [Thread-3 (_refresh_task)] - Creating placeholder data for ^IMOEX.ME: Empty or insufficient historical data
2025-06-21 03:36:08,450 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^GDAXI
2025-06-21 03:36:08,659 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^BSESN
2025-06-21 03:36:09,013 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^VNI
2025-06-21 03:36:09,034 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^N225
2025-06-21 03:36:09,241 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^NSEI
2025-06-21 03:36:09,608 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^VN30
2025-06-21 03:36:09,618 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^FTSE
2025-06-21 03:36:09,824 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^BVSP
2025-06-21 03:36:10,189 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^GDAXI
2025-06-21 03:36:10,205 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^FCHI
2025-06-21 03:36:10,478 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^MXX
2025-06-21 03:36:10,777 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^N225
2025-06-21 03:36:10,932 - ERROR - [Thread-2 (_refresh_task)] - $^FTMIB: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:36:10,933 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for ^FTMIB: Empty or insufficient historical data
2025-06-21 03:36:11,064 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^AXJO
2025-06-21 03:36:11,379 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^FTSE
2025-06-21 03:36:11,568 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^KS11
2025-06-21 03:36:11,689 - ERROR - [Thread-1 (_refresh_task)] - $^XU100: possibly delisted; no price data found  (period=2d)
2025-06-21 03:36:11,691 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^XU100: Empty or insufficient historical data
2025-06-21 03:36:11,980 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^FCHI
2025-06-21 03:36:12,151 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^BSESN
2025-06-21 03:36:12,730 - ERROR - [Thread-3 (_refresh_task)] - $^FTMIB: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:36:12,748 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^NSEI
2025-06-21 03:36:12,748 - WARNING - [Thread-3 (_refresh_task)] - Creating placeholder data for ^FTMIB: Empty or insufficient historical data
2025-06-21 03:36:12,805 - ERROR - [Thread-1 (_refresh_task)] - $^TASI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:36:12,807 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^TASI: Empty or insufficient historical data
2025-06-21 03:36:13,334 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^BVSP
2025-06-21 03:36:13,404 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^KS11
2025-06-21 03:36:13,943 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^MXX
2025-06-21 03:36:13,947 - ERROR - [Thread-1 (_refresh_task)] - $^ADI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:36:13,950 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^ADI: Empty or insufficient historical data
2025-06-21 03:36:14,003 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^BSESN
2025-06-21 03:36:14,593 - INFO - [Thread-2 (_refresh_task)] - Successfully fetched data for ^AXJO
2025-06-21 03:36:14,612 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^NSEI
2025-06-21 03:36:14,614 - ERROR - [Thread-1 (_refresh_task)] - $^QSI: possibly delisted; no price data found  (period=2d)
2025-06-21 03:36:14,615 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^QSI: Empty or insufficient historical data
2025-06-21 03:36:15,200 - ERROR - [Thread-2 (_refresh_task)] - $^XU100: possibly delisted; no price data found  (period=2d)
2025-06-21 03:36:15,201 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for ^XU100: Empty or insufficient historical data
2025-06-21 03:36:15,260 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^BVSP
2025-06-21 03:36:15,760 - ERROR - [Thread-1 (_refresh_task)] - $^KWTI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:36:15,761 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^KWTI: Empty or insufficient historical data
2025-06-21 03:36:15,762 - INFO - [Thread-1 (_refresh_task)] - Caching data for INSTANT access: indices
2025-06-21 03:36:15,764 - INFO - [Thread-1 (_refresh_task)] - Caching data for key: indices_data
2025-06-21 03:36:15,896 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^MXX
2025-06-21 03:36:15,980 - ERROR - [Thread-2 (_refresh_task)] - $^TASI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:36:15,982 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for ^TASI: Empty or insufficient historical data
2025-06-21 03:36:16,530 - INFO - [Thread-3 (_refresh_task)] - Successfully fetched data for ^AXJO
2025-06-21 03:36:16,784 - ERROR - [Thread-2 (_refresh_task)] - $^ADI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:36:16,786 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for ^ADI: Empty or insufficient historical data
2025-06-21 03:36:17,155 - ERROR - [Thread-3 (_refresh_task)] - $^XU100: possibly delisted; no price data found  (period=2d)
2025-06-21 03:36:17,156 - WARNING - [Thread-3 (_refresh_task)] - Creating placeholder data for ^XU100: Empty or insufficient historical data
2025-06-21 03:36:17,362 - ERROR - [Thread-2 (_refresh_task)] - $^QSI: possibly delisted; no price data found  (period=2d)
2025-06-21 03:36:17,363 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for ^QSI: Empty or insufficient historical data
2025-06-21 03:36:17,714 - INFO - [Thread-1 (_refresh_task)] - Fetching commodities data...
2025-06-21 03:36:17,933 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:17,974 - ERROR - [Thread-3 (_refresh_task)] - $^TASI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:36:17,977 - WARNING - [Thread-3 (_refresh_task)] - Creating placeholder data for ^TASI: Empty or insufficient historical data
2025-06-21 03:36:18,157 - ERROR - [Thread-2 (_refresh_task)] - $^KWTI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:36:18,159 - WARNING - [Thread-2 (_refresh_task)] - Creating placeholder data for ^KWTI: Empty or insufficient historical data
2025-06-21 03:36:18,159 - INFO - [Thread-2 (_refresh_task)] - Caching data for INSTANT access: indices
2025-06-21 03:36:18,160 - INFO - [Thread-2 (_refresh_task)] - Caching data for key: indices_data
2025-06-21 03:36:18,180 - INFO - [Thread-2 (_refresh_task)] - Fetching commodities data...
2025-06-21 03:36:18,695 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:18,695 - ERROR - [Thread-3 (_refresh_task)] - $^ADI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:36:18,698 - WARNING - [Thread-3 (_refresh_task)] - Creating placeholder data for ^ADI: Empty or insufficient historical data
2025-06-21 03:36:19,338 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:19,357 - ERROR - [Thread-3 (_refresh_task)] - $^QSI: possibly delisted; no price data found  (period=2d)
2025-06-21 03:36:19,358 - WARNING - [Thread-3 (_refresh_task)] - Creating placeholder data for ^QSI: Empty or insufficient historical data
2025-06-21 03:36:19,871 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:20,103 - ERROR - [Thread-3 (_refresh_task)] - $^KWTI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:36:20,104 - WARNING - [Thread-3 (_refresh_task)] - Creating placeholder data for ^KWTI: Empty or insufficient historical data
2025-06-21 03:36:20,105 - INFO - [Thread-3 (_refresh_task)] - Caching data for INSTANT access: indices
2025-06-21 03:36:20,105 - INFO - [Thread-3 (_refresh_task)] - Caching data for key: indices_data
2025-06-21 03:36:20,135 - INFO - [Thread-3 (_refresh_task)] - Fetching commodities data...
2025-06-21 03:36:20,397 - ERROR - [Thread-3 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:20,408 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:20,900 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:20,900 - ERROR - [Thread-3 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:21,405 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:21,424 - ERROR - [Thread-3 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:21,723 - ERROR - [Thread-2 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:21,987 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:21,987 - ERROR - [Thread-3 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:22,078 - INFO - [Thread-2 (_refresh_task)] - Caching data for key: commodities_data
2025-06-21 03:36:22,083 - INFO - [Thread-2 (_refresh_task)] - Fetching major stocks data for all countries...
2025-06-21 03:36:22,234 - INFO - [Thread-1 (_refresh_task)] - Caching data for key: commodities_data
2025-06-21 03:36:22,352 - INFO - [Thread-1 (_refresh_task)] - Fetching major stocks data for all countries...
2025-06-21 03:36:22,526 - ERROR - [Thread-3 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:22,976 - ERROR - [Thread-3 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:22,982 - ERROR - [Thread-2 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:23,337 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:23,495 - ERROR - [Thread-3 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:23,950 - ERROR - [Thread-3 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:24,124 - INFO - [Thread-3 (_refresh_task)] - Caching data for key: commodities_data
2025-06-21 03:36:24,429 - ERROR - [Thread-2 (_refresh_task)] - HTTP Error 500: 
2025-06-21 03:36:24,606 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:24,814 - INFO - [Thread-3 (_refresh_task)] - Fetching major stocks data for all countries...
2025-06-21 03:36:25,771 - ERROR - [Thread-2 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:25,790 - ERROR - [Thread-3 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:25,976 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 500: 
2025-06-21 03:36:26,611 - INFO - [Thread-4 (_refresh_news_task)] - Fetching stock market news from The Globe and Mail and other sources...
2025-06-21 03:36:26,660 - INFO - [Thread-4 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/business/
2025-06-21 03:36:27,583 - ERROR - [Thread-3 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:27,584 - ERROR - [Thread-2 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:27,643 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 500: 
2025-06-21 03:36:27,991 - INFO - [Thread-4 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/investing/
2025-06-21 03:36:29,150 - INFO - [Thread-5 (_refresh_news_task)] - Fetching stock market news from The Globe and Mail and other sources...
2025-06-21 03:36:29,223 - INFO - [Thread-5 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/business/
2025-06-21 03:36:29,299 - INFO - [Thread-4 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/section/report-on-business/
2025-06-21 03:36:29,759 - INFO - [Thread-6 (_refresh_news_task)] - Fetching stock market news from The Globe and Mail and other sources...
2025-06-21 03:36:30,191 - INFO - [Thread-6 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/business/
2025-06-21 03:36:30,582 - INFO - [Thread-4 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/economy/
2025-06-21 03:36:31,563 - INFO - [Thread-5 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/investing/
2025-06-21 03:36:32,198 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:32,290 - ERROR - [Thread-2 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:32,308 - ERROR - [Thread-3 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:32,390 - INFO - [Thread-4 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/section/globe-investor/
2025-06-21 03:36:32,646 - INFO - [Thread-6 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/investing/
2025-06-21 03:36:33,844 - INFO - [Thread-5 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/section/report-on-business/
2025-06-21 03:36:34,243 - INFO - [Thread-4 (_refresh_news_task)] - Parsing RSS feed: https://feeds.reuters.com/reuters/businessNews
2025-06-21 03:36:34,610 - INFO - [Thread-4 (_refresh_news_task)] - Parsing RSS feed: https://feeds.reuters.com/news/economy
2025-06-21 03:36:35,152 - INFO - [Thread-4 (_refresh_news_task)] - Parsing RSS feed: https://www.cnbc.com/id/100003114/device/rss/rss.html
2025-06-21 03:36:35,166 - INFO - [Thread-6 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/section/report-on-business/
2025-06-21 03:36:35,169 - INFO - [Thread-5 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/economy/
2025-06-21 03:36:35,948 - INFO - [Thread-5 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/section/globe-investor/
2025-06-21 03:36:36,261 - INFO - [Thread-6 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/economy/
2025-06-21 03:36:36,491 - ERROR - [Thread-3 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:36,526 - INFO - [Thread-4 (_refresh_news_task)] - Parsing RSS feed: https://www.cnbc.com/id/10000664/device/rss/rss.html
2025-06-21 03:36:36,809 - INFO - [Thread-6 (_refresh_news_task)] - Parsing The Globe and Mail RSS feed: https://www.theglobeandmail.com/arc/outboundfeeds/rss/section/globe-investor/
2025-06-21 03:36:37,131 - INFO - [Thread-5 (_refresh_news_task)] - Parsing RSS feed: https://feeds.reuters.com/reuters/businessNews
2025-06-21 03:36:37,378 - INFO - [Thread-5 (_refresh_news_task)] - Parsing RSS feed: https://feeds.reuters.com/news/economy
2025-06-21 03:36:37,588 - ERROR - [Thread-2 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:37,589 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 500: 
2025-06-21 03:36:37,590 - INFO - [Thread-5 (_refresh_news_task)] - Parsing RSS feed: https://www.cnbc.com/id/100003114/device/rss/rss.html
2025-06-21 03:36:37,818 - INFO - [Thread-6 (_refresh_news_task)] - Parsing RSS feed: https://feeds.reuters.com/reuters/businessNews
2025-06-21 03:36:38,155 - INFO - [Thread-6 (_refresh_news_task)] - Parsing RSS feed: https://feeds.reuters.com/news/economy
2025-06-21 03:36:38,200 - INFO - [Thread-4 (_refresh_news_task)] - Parsing RSS feed: https://feeds.bloomberg.com/markets/news.rss
2025-06-21 03:36:38,424 - INFO - [Thread-6 (_refresh_news_task)] - Parsing RSS feed: https://www.cnbc.com/id/100003114/device/rss/rss.html
2025-06-21 03:36:38,813 - INFO - [Thread-5 (_refresh_news_task)] - Parsing RSS feed: https://www.cnbc.com/id/10000664/device/rss/rss.html
2025-06-21 03:36:39,148 - INFO - [Thread-4 (_refresh_news_task)] - Parsing RSS feed: https://feeds.finance.yahoo.com/rss/2.0/headline
2025-06-21 03:36:39,357 - INFO - [Thread-6 (_refresh_news_task)] - Parsing RSS feed: https://www.cnbc.com/id/10000664/device/rss/rss.html
2025-06-21 03:36:39,543 - ERROR - [Thread-3 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:39,577 - INFO - [Thread-5 (_refresh_news_task)] - Parsing RSS feed: https://feeds.bloomberg.com/markets/news.rss
2025-06-21 03:36:39,924 - INFO - [Thread-6 (_refresh_news_task)] - Parsing RSS feed: https://feeds.bloomberg.com/markets/news.rss
2025-06-21 03:36:40,171 - INFO - [Thread-4 (_refresh_news_task)] - Parsing RSS feed: https://www.marketwatch.com/rss/topstories
2025-06-21 03:36:40,388 - INFO - [Thread-5 (_refresh_news_task)] - Parsing RSS feed: https://feeds.finance.yahoo.com/rss/2.0/headline
2025-06-21 03:36:40,654 - INFO - [Thread-6 (_refresh_news_task)] - Parsing RSS feed: https://feeds.finance.yahoo.com/rss/2.0/headline
2025-06-21 03:36:40,656 - ERROR - [Thread-2 (_refresh_task)] - HTTP Error 500: 
2025-06-21 03:36:40,751 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 500: 
2025-06-21 03:36:40,887 - INFO - [Thread-5 (_refresh_news_task)] - Parsing RSS feed: https://www.marketwatch.com/rss/topstories
2025-06-21 03:36:41,284 - INFO - [Thread-6 (_refresh_news_task)] - Parsing RSS feed: https://www.marketwatch.com/rss/topstories
2025-06-21 03:36:41,641 - INFO - [Thread-4 (_refresh_news_task)] - Parsing RSS feed: https://www.ft.com/rss/home/<USER>
2025-06-21 03:36:41,893 - INFO - [Thread-5 (_refresh_news_task)] - Parsing RSS feed: https://www.ft.com/rss/home/<USER>
2025-06-21 03:36:42,135 - ERROR - [Thread-3 (_refresh_task)] - HTTP Error 500: 
2025-06-21 03:36:42,296 - INFO - [Thread-6 (_refresh_news_task)] - Parsing RSS feed: https://www.ft.com/rss/home/<USER>
2025-06-21 03:36:42,491 - INFO - [Thread-4 (_refresh_news_task)] - Parsing RSS feed: https://www.ft.com/rss/home/<USER>
2025-06-21 03:36:42,717 - INFO - [Thread-5 (_refresh_news_task)] - Parsing RSS feed: https://www.ft.com/rss/home/<USER>
2025-06-21 03:36:43,088 - ERROR - [Thread-2 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:43,088 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:43,286 - INFO - [Thread-6 (_refresh_news_task)] - Parsing RSS feed: https://www.ft.com/rss/home/<USER>
2025-06-21 03:36:43,467 - INFO - [Thread-4 (_refresh_news_task)] - Parsing RSS feed: https://www.ft.com/rss/home/<USER>
2025-06-21 03:36:43,467 - INFO - [Thread-5 (_refresh_news_task)] - Parsing RSS feed: https://www.ft.com/rss/home/<USER>
2025-06-21 03:36:44,146 - ERROR - [Thread-3 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:44,177 - INFO - [Thread-6 (_refresh_news_task)] - Parsing RSS feed: https://www.ft.com/rss/home/<USER>
2025-06-21 03:36:44,386 - INFO - [Thread-4 (_refresh_news_task)] - Successfully fetched 42 stock market news articles
2025-06-21 03:36:44,386 - INFO - [Thread-5 (_refresh_news_task)] - Successfully fetched 42 stock market news articles
2025-06-21 03:36:44,387 - INFO - [Thread-4 (_refresh_news_task)] - The Globe and Mail articles: 30, Other sources: 12
2025-06-21 03:36:44,390 - INFO - [Thread-5 (_refresh_news_task)] - The Globe and Mail articles: 30, Other sources: 12
2025-06-21 03:36:44,391 - INFO - [Thread-4 (_refresh_news_task)] - Caching data for key: news
2025-06-21 03:36:44,392 - INFO - [Thread-5 (_refresh_news_task)] - Caching data for key: news
2025-06-21 03:36:44,702 - INFO - [Thread-6 (_refresh_news_task)] - Successfully fetched 42 stock market news articles
2025-06-21 03:36:44,702 - INFO - [Thread-6 (_refresh_news_task)] - The Globe and Mail articles: 30, Other sources: 12
2025-06-21 03:36:44,703 - INFO - [Thread-6 (_refresh_news_task)] - Caching data for key: news
2025-06-21 03:36:44,862 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:45,024 - ERROR - [Thread-2 (_refresh_task)] - HTTP Error 500: 
2025-06-21 03:36:45,533 - ERROR - [Thread-3 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:46,237 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 500: 
2025-06-21 03:36:46,263 - ERROR - [Thread-2 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:46,900 - ERROR - [Thread-3 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:47,548 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 500: 
2025-06-21 03:36:47,559 - ERROR - [Thread-2 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:48,201 - ERROR - [Thread-3 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:48,857 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 500: 
2025-06-21 03:36:49,052 - ERROR - [Thread-2 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:49,564 - ERROR - [Thread-3 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:50,228 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:50,412 - ERROR - [Thread-2 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:50,932 - ERROR - [Thread-3 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:51,669 - ERROR - [Thread-2 (_refresh_task)] - HTTP Error 500: 
2025-06-21 03:36:51,695 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:52,356 - ERROR - [Thread-3 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:53,102 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:36:53,103 - ERROR - [Thread-2 (_refresh_task)] - HTTP Error 401: 
