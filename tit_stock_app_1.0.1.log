2025-06-21 03:45:14,062 - INFO - [MainThread] - TiT Stock App 1.0.1 Application Starting...
2025-06-21 03:45:14,063 - INFO - [MainThread] - Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
2025-06-21 03:45:14,063 - INFO - [MainThread] - Configuration loaded successfully.
2025-06-21 03:45:16,070 - INFO - [MainThread] - TiT Stock App main initialization started...
2025-06-21 03:45:16,072 - INFO - [MainThread] - CacheService initialized.
2025-06-21 03:45:16,072 - INFO - [MainThread] - StockDataService initialized.
2025-06-21 03:45:16,072 - INFO - [MainThread] - StockAIService initialized with Gemini 1.5 Flash and working API key.
2025-06-21 03:45:16,073 - INFO - [MainThread] - Setting up UI components...
2025-06-21 03:45:17,656 - INFO - [MainThread] - UI setup complete.
2025-06-21 03:45:17,657 - INFO - [MainThread] - Ti<PERSON> <PERSON> App initialized successfully.
2025-06-21 03:45:18,189 - INFO - [Thread-1 (_refresh_task)] - Fetching stock indices data with rate limiting...
2025-06-21 03:45:19,274 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^GSPC
2025-06-21 03:45:19,886 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^DJI
2025-06-21 03:45:20,496 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^IXIC
2025-06-21 03:45:21,107 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^GSPTSE
2025-06-21 03:45:21,705 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for 000001.SS
2025-06-21 03:45:22,287 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for 399001.SZ
2025-06-21 03:45:23,268 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 404: 
2025-06-21 03:45:23,675 - ERROR - [Thread-1 (_refresh_task)] - $^IMOEX.ME: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:45:23,678 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^IMOEX.ME: Empty or insufficient historical data
2025-06-21 03:45:24,267 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^VNI
2025-06-21 03:45:24,928 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^VN30
2025-06-21 03:45:25,522 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^GDAXI
2025-06-21 03:45:26,108 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^N225
2025-06-21 03:45:26,736 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^FTSE
2025-06-21 03:45:27,324 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^FCHI
2025-06-21 03:45:28,339 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:45:28,756 - ERROR - [Thread-1 (_refresh_task)] - $^FTMIB: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:45:28,757 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^FTMIB: Empty or insufficient historical data
2025-06-21 03:45:29,346 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^KS11
2025-06-21 03:45:29,928 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^BSESN
2025-06-21 03:45:30,517 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^NSEI
2025-06-21 03:45:31,101 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^BVSP
2025-06-21 03:45:31,778 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^MXX
2025-06-21 03:45:32,366 - INFO - [Thread-1 (_refresh_task)] - Successfully fetched data for ^AXJO
2025-06-21 03:45:32,987 - ERROR - [Thread-1 (_refresh_task)] - $^XU100: possibly delisted; no price data found  (period=2d)
2025-06-21 03:45:32,988 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^XU100: Empty or insufficient historical data
2025-06-21 03:45:34,103 - ERROR - [Thread-1 (_refresh_task)] - $^TASI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:45:34,104 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^TASI: Empty or insufficient historical data
2025-06-21 03:45:35,100 - ERROR - [Thread-1 (_refresh_task)] - $^ADI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:45:35,101 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^ADI: Empty or insufficient historical data
2025-06-21 03:45:35,729 - ERROR - [Thread-1 (_refresh_task)] - $^QSI: possibly delisted; no price data found  (period=2d)
2025-06-21 03:45:35,731 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^QSI: Empty or insufficient historical data
2025-06-21 03:45:36,726 - ERROR - [Thread-1 (_refresh_task)] - $^KWTI: possibly delisted; no price data found  (period=2d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:45:36,731 - WARNING - [Thread-1 (_refresh_task)] - Creating placeholder data for ^KWTI: Empty or insufficient historical data
2025-06-21 03:45:36,732 - INFO - [Thread-1 (_refresh_task)] - Caching data for INSTANT access: indices
2025-06-21 03:45:36,733 - INFO - [Thread-1 (_refresh_task)] - Caching data for key: indices_data
2025-06-21 03:45:36,744 - INFO - [Thread-1 (_refresh_task)] - Fetching commodities data...
2025-06-21 03:45:37,043 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:45:38,309 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:45:39,876 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:45:40,448 - INFO - [Thread-1 (_refresh_task)] - Caching data for key: commodities_data
2025-06-21 03:45:40,647 - INFO - [Thread-1 (_refresh_task)] - Fetching major stocks data for all countries...
2025-06-21 03:45:41,585 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 500: 
2025-06-21 03:45:42,982 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:45:44,402 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:45:45,841 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 500: 
2025-06-21 03:45:47,124 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 500: 
2025-06-21 03:45:48,590 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:45:49,874 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:45:51,205 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:45:52,558 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:45:53,938 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:45:55,374 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:45:56,818 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 500: 
2025-06-21 03:45:58,239 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:45:59,636 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:46:01,091 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:46:02,451 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 500: 
2025-06-21 03:46:03,905 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:46:05,351 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:46:06,650 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:46:08,060 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:46:09,582 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:46:10,961 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 500: 
2025-06-21 03:46:12,288 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
2025-06-21 03:46:13,702 - ERROR - [Thread-1 (_refresh_task)] - HTTP Error 401: 
