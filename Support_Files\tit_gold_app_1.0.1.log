2025-06-21 03:47:14,726 - INFO - [MainThread] - TiT Gold App 1.0.1 Starting...
2025-06-21 03:47:14,726 - INFO - [MainThread] - Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
2025-06-21 03:47:16,675 - INFO - [MainThread] - TiT Gold App main initialization started...
2025-06-21 03:47:16,676 - INFO - [MainThread] - GoldCacheService initialized.
2025-06-21 03:47:16,676 - INFO - [MainThread] - GoldDataService initialized with comprehensive precious metals coverage.
2025-06-21 03:47:16,677 - INFO - [MainThread] - GoldAIService initialized with Gemini Pro.
2025-06-21 03:47:17,635 - INFO - [MainThread] - Gold app UI setup complete
2025-06-21 03:47:17,637 - INFO - [MainThread] - TiT Gold App 1.0.1 initialized successfully.
2025-06-21 03:47:17,759 - INFO - [Thread-1 (refresh_worker)] - Fetching real-time precious metals prices...
2025-06-21 03:47:20,151 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:47:20,849 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:47:23,549 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:47:24,141 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:47:24,827 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:47:25,526 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:47:26,653 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:47:27,300 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:47:27,535 - INFO - [Thread-1 (refresh_worker)] - Caching data for key: gold_prices
2025-06-21 03:47:27,536 - INFO - [Thread-1 (refresh_worker)] - Fetching gold miners data for all regions...
2025-06-21 03:47:27,805 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:47:28,240 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:47:28,874 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:47:30,013 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:47:31,125 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:47:32,522 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:47:33,986 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:47:34,663 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:47:39,460 - ERROR - [Thread-1 (refresh_worker)] - $DEG.AX: possibly delisted; no price data found  (period=1d)
2025-06-21 03:47:40,337 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:47:42,416 - ERROR - [Thread-1 (refresh_worker)] - $ROSN.ME: possibly delisted; no price data found  (period=1d)
2025-06-21 03:47:42,684 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:47:42,978 - ERROR - [Thread-1 (refresh_worker)] - $GAZP.ME: possibly delisted; no price data found  (period=1d)
2025-06-21 03:47:43,260 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:47:44,700 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
