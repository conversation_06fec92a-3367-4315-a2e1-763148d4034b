# Smooth Loading System for TiT Suite
# Ultra-smooth loading animations and progress tracking
# Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.

import tkinter as tk
from tkinter import ttk
import threading
import time
import logging
from typing import Callable, Optional, Any
import math

class SmoothProgressBar:
    """Ultra-smooth animated progress bar with gradient effects"""
    
    def __init__(self, parent, width: int = 400, height: int = 20):
        self.parent = parent
        self.width = width
        self.height = height
        self.progress = 0.0
        self.target_progress = 0.0
        self.animation_speed = 0.02
        self.is_animating = False
        
        # Create canvas for custom progress bar
        self.canvas = tk.Canvas(
            parent, 
            width=width, 
            height=height, 
            highlightthickness=0,
            bg='#F0F0F0'
        )
        self.canvas.pack(pady=10)
        
        # Animation thread
        self.animation_thread = None
        self.stop_animation = False
        
        logging.info("✅ SmoothProgressBar initialized")
    
    def set_progress(self, progress: float, animate: bool = True):
        """Set progress with smooth animation"""
        self.target_progress = max(0.0, min(1.0, progress))
        
        if animate and not self.is_animating:
            self._start_animation()
        elif not animate:
            self.progress = self.target_progress
            self._draw_progress()
    
    def _start_animation(self):
        """Start smooth animation thread"""
        if self.animation_thread and self.animation_thread.is_alive():
            return
        
        self.is_animating = True
        self.stop_animation = False
        self.animation_thread = threading.Thread(target=self._animate_progress)
        self.animation_thread.daemon = True
        self.animation_thread.start()
    
    def _animate_progress(self):
        """Animate progress smoothly"""
        while self.is_animating and not self.stop_animation:
            if abs(self.progress - self.target_progress) < 0.001:
                self.progress = self.target_progress
                self.is_animating = False
                break
            
            # Smooth easing animation
            diff = self.target_progress - self.progress
            self.progress += diff * self.animation_speed
            
            # Update UI on main thread
            self.parent.after(0, self._draw_progress)
            
            time.sleep(0.016)  # ~60 FPS
    
    def _draw_progress(self):
        """Draw the progress bar with gradient effect"""
        self.canvas.delete("all")
        
        # Background
        self.canvas.create_rectangle(
            0, 0, self.width, self.height,
            fill='#E0E0E0', outline='#CCCCCC', width=1
        )
        
        # Progress fill with gradient effect
        if self.progress > 0:
            fill_width = int(self.width * self.progress)
            
            # Create gradient effect
            for i in range(fill_width):
                ratio = i / self.width
                # Blue to green gradient
                r = int(59 + (76 - 59) * ratio)
                g = int(130 + (175 - 130) * ratio)
                b = int(246 + (80 - 246) * ratio)
                color = f"#{r:02x}{g:02x}{b:02x}"
                
                self.canvas.create_line(
                    i, 0, i, self.height,
                    fill=color, width=1
                )
        
        # Progress text
        percentage = int(self.progress * 100)
        self.canvas.create_text(
            self.width // 2, self.height // 2,
            text=f"{percentage}%",
            fill='white' if self.progress > 0.5 else 'black',
            font=('Segoe UI', 10, 'bold')
        )
    
    def destroy(self):
        """Clean up resources"""
        self.stop_animation = True
        self.is_animating = False
        if self.animation_thread:
            self.animation_thread.join(timeout=1.0)
        self.canvas.destroy()

class LoadingSpinner:
    """Smooth rotating loading spinner"""
    
    def __init__(self, parent, size: int = 40):
        self.parent = parent
        self.size = size
        self.angle = 0
        self.is_spinning = False
        
        self.canvas = tk.Canvas(
            parent,
            width=size,
            height=size,
            highlightthickness=0,
            bg=parent.cget('bg') if hasattr(parent, 'cget') else 'white'
        )
        self.canvas.pack(pady=5)
        
        self.spin_thread = None
        self.stop_spinning = False
    
    def start_spinning(self):
        """Start the spinning animation"""
        if self.is_spinning:
            return
        
        self.is_spinning = True
        self.stop_spinning = False
        self.spin_thread = threading.Thread(target=self._spin_animation)
        self.spin_thread.daemon = True
        self.spin_thread.start()
    
    def stop_spinning(self):
        """Stop the spinning animation"""
        self.stop_spinning = True
        self.is_spinning = False
        if self.spin_thread:
            self.spin_thread.join(timeout=1.0)
        self.canvas.delete("all")
    
    def _spin_animation(self):
        """Animate the spinner"""
        while self.is_spinning and not self.stop_spinning:
            self.angle = (self.angle + 10) % 360
            self.parent.after(0, self._draw_spinner)
            time.sleep(0.05)  # 20 FPS
    
    def _draw_spinner(self):
        """Draw the spinning animation"""
        self.canvas.delete("all")
        
        center = self.size // 2
        radius = center - 5
        
        # Draw spinning arcs
        for i in range(8):
            start_angle = (self.angle + i * 45) % 360
            alpha = 1.0 - (i * 0.1)
            
            # Calculate color with alpha
            color_value = int(255 * alpha)
            color = f"#{color_value:02x}{color_value:02x}{color_value:02x}"
            
            self.canvas.create_arc(
                center - radius, center - radius,
                center + radius, center + radius,
                start=start_angle, extent=30,
                outline=color, width=3, style='arc'
            )

class SmoothLoader:
    """Complete smooth loading system with progress tracking"""
    
    def __init__(self, parent, title: str = "Loading..."):
        self.parent = parent
        self.title = title
        self.loading_window = None
        self.progress_bar = None
        self.spinner = None
        self.status_label = None
        self.current_step = 0
        self.total_steps = 0
        
    def show_loading(self, steps: list, callback: Optional[Callable] = None):
        """Show loading window with smooth progress"""
        self.total_steps = len(steps)
        self.current_step = 0
        
        # Create loading window
        self.loading_window = tk.Toplevel(self.parent)
        self.loading_window.title(self.title)
        self.loading_window.geometry("500x200")
        self.loading_window.resizable(False, False)
        self.loading_window.configure(bg='white')
        
        # Center the window
        self.loading_window.transient(self.parent)
        self.loading_window.grab_set()
        
        # Title
        title_label = tk.Label(
            self.loading_window,
            text=self.title,
            font=('Segoe UI', 16, 'bold'),
            bg='white',
            fg='#333333'
        )
        title_label.pack(pady=20)
        
        # Spinner
        self.spinner = LoadingSpinner(self.loading_window, size=50)
        self.spinner.start_spinning()
        
        # Progress bar
        self.progress_bar = SmoothProgressBar(self.loading_window, width=400, height=25)
        
        # Status label
        self.status_label = tk.Label(
            self.loading_window,
            text="Initializing...",
            font=('Segoe UI', 10),
            bg='white',
            fg='#666666'
        )
        self.status_label.pack(pady=10)
        
        # Start loading process
        self._execute_steps(steps, callback)
    
    def _execute_steps(self, steps: list, callback: Optional[Callable]):
        """Execute loading steps with progress updates"""
        def run_steps():
            try:
                for i, (step_name, step_function) in enumerate(steps):
                    # Update status
                    self.parent.after(0, lambda name=step_name: self.update_status(name))
                    
                    # Execute step
                    if callable(step_function):
                        step_function()
                    else:
                        time.sleep(0.5)  # Simulate work
                    
                    # Update progress
                    progress = (i + 1) / self.total_steps
                    self.parent.after(0, lambda p=progress: self.progress_bar.set_progress(p))
                    
                    time.sleep(0.2)  # Small delay for smooth animation
                
                # Complete
                self.parent.after(0, lambda: self.update_status("Complete!"))
                time.sleep(0.5)
                
                # Close loading window
                self.parent.after(0, self.hide_loading)
                
                # Execute callback
                if callback:
                    self.parent.after(0, callback)
                    
            except Exception as e:
                logging.error(f"❌ Loading step failed: {e}")
                self.parent.after(0, lambda: self.update_status(f"Error: {e}"))
        
        # Run in separate thread
        loading_thread = threading.Thread(target=run_steps)
        loading_thread.daemon = True
        loading_thread.start()
    
    def update_status(self, status: str):
        """Update status text"""
        if self.status_label:
            self.status_label.config(text=status)
    
    def hide_loading(self):
        """Hide loading window"""
        if self.spinner:
            self.spinner.stop_spinning()
        
        if self.loading_window:
            self.loading_window.grab_release()
            self.loading_window.destroy()
            self.loading_window = None

# Convenience functions
def show_smooth_loading(parent, title: str, steps: list, callback: Optional[Callable] = None):
    """Show smooth loading with steps"""
    loader = SmoothLoader(parent, title)
    loader.show_loading(steps, callback)
    return loader

def create_progress_bar(parent, width: int = 400, height: int = 20) -> SmoothProgressBar:
    """Create a smooth progress bar"""
    return SmoothProgressBar(parent, width, height)

def create_spinner(parent, size: int = 40) -> LoadingSpinner:
    """Create a loading spinner"""
    return LoadingSpinner(parent, size)

logging.info("✅ Smooth loading system loaded successfully!")
