2025-06-21 03:12:41,426 - INFO - [MainThread] - TiT Defense App 1.0.1 Starting...
2025-06-21 03:12:41,426 - INFO - [MainThread] - Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
2025-06-21 03:12:43,496 - INFO - [MainThread] - TiT Defense App main initialization started...
2025-06-21 03:12:43,497 - INFO - [MainThread] - DefenseCacheService initialized.
2025-06-21 03:12:43,497 - INFO - [MainThread] - DefenseDataService initialized with comprehensive defense coverage.
2025-06-21 03:12:43,498 - INFO - [MainThread] - DefenseAIService initialized with Gemini Pro.
2025-06-21 03:12:44,625 - INFO - [MainThread] - Defense app UI setup complete
2025-06-21 03:12:44,627 - INFO - [MainThread] - TiT Defense App 1.0.1 initialized successfully.
2025-06-21 03:12:45,140 - INFO - [Thread-1 (refresh_worker)] - Fetching defense stocks data for all regions...
2025-06-21 03:12:45,840 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:12:46,814 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:12:47,589 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:12:48,380 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:12:49,228 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:12:49,981 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:12:50,718 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:12:51,341 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:12:51,891 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:12:52,363 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:12:52,916 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:12:53,636 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:12:54,348 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:12:55,014 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:12:55,568 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:12:56,716 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:12:57,439 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:12:58,141 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:12:58,907 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:12:59,580 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:00,883 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:01,426 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:01,953 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:03,642 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:04,387 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:04,696 - ERROR - [Thread-1 (refresh_worker)] - $BAE.L: possibly delisted; no price data found  (period=1d)
2025-06-21 03:13:05,705 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 404: 
2025-06-21 03:13:06,366 - ERROR - [Thread-1 (refresh_worker)] - $MEGG.L: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:13:06,714 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:07,403 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:08,146 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:08,907 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:09,267 - ERROR - [Thread-1 (refresh_worker)] - $THA.PA: possibly delisted; no price data found  (period=1d)
2025-06-21 03:13:09,656 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:10,447 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:11,163 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:11,857 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:12,534 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:13,568 - ERROR - [Thread-1 (refresh_worker)] - $FNC.MI: possibly delisted; no price data found  (period=1d)
2025-06-21 03:13:14,408 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:15,654 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:17,169 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:17,892 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:18,596 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:19,246 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:19,954 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:20,328 - INFO - [Thread-2 (refresh_worker)] - Fetching defense stocks data for all regions...
2025-06-21 03:13:21,145 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:21,885 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:22,926 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:23,005 - ERROR - [Thread-1 (refresh_worker)] - $UNAC.ME: possibly delisted; no price data found  (period=1d)
2025-06-21 03:13:23,409 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:23,805 - ERROR - [Thread-1 (refresh_worker)] - $AFKS.ME: possibly delisted; no price data found  (period=1d)
2025-06-21 03:13:24,027 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:24,249 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:25,075 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:25,589 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:26,002 - ERROR - [Thread-1 (refresh_worker)] - $600893.SZ: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:13:26,145 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:26,385 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:26,819 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:27,491 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:27,671 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:28,470 - ERROR - [Thread-1 (refresh_worker)] - $ELBT.TA: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:13:28,471 - INFO - [Thread-1 (refresh_worker)] - Caching data for key: defense_stocks
2025-06-21 03:13:28,472 - INFO - [Thread-1 (refresh_worker)] - Fetching defense sector ETFs data...
2025-06-21 03:13:28,840 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:28,857 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:29,764 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:30,531 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:31,175 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:31,201 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:31,949 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:32,150 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:32,745 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:34,283 - INFO - [Thread-1 (refresh_worker)] - Caching data for key: defense_etfs
2025-06-21 03:13:34,284 - INFO - [Thread-1 (refresh_worker)] - Fetching conflict status and geopolitical data...
2025-06-21 03:13:34,285 - INFO - [Thread-1 (refresh_worker)] - Caching data for key: conflict_data
2025-06-21 03:13:34,285 - INFO - [Thread-1 (refresh_worker)] - Fetching arms trade data...
2025-06-21 03:13:34,286 - INFO - [Thread-1 (refresh_worker)] - Caching data for key: arms_trade
2025-06-21 03:13:35,390 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:35,620 - ERROR - [Thread-2 (refresh_worker)] - $BAE.L: possibly delisted; no price data found  (period=1d)
2025-06-21 03:13:36,013 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:36,582 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 404: 
2025-06-21 03:13:36,958 - ERROR - [Thread-2 (refresh_worker)] - $MEGG.L: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:13:38,016 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:39,091 - ERROR - [Thread-2 (refresh_worker)] - $THA.PA: possibly delisted; no price data found  (period=1d)
2025-06-21 03:13:41,616 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:41,771 - ERROR - [Thread-2 (refresh_worker)] - $FNC.MI: possibly delisted; no price data found  (period=1d)
2025-06-21 03:13:42,158 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:42,760 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:43,310 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:43,933 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:44,589 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:45,220 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:45,853 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:46,406 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:47,084 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:47,788 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:48,431 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:49,045 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:49,649 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:50,322 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:50,609 - ERROR - [Thread-2 (refresh_worker)] - $UNAC.ME: possibly delisted; no price data found  (period=1d)
2025-06-21 03:13:51,112 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:51,371 - ERROR - [Thread-2 (refresh_worker)] - $AFKS.ME: possibly delisted; no price data found  (period=1d)
2025-06-21 03:13:51,757 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:52,430 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:52,865 - ERROR - [Thread-2 (refresh_worker)] - $600893.SZ: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:13:54,111 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:13:54,457 - ERROR - [Thread-2 (refresh_worker)] - $ELBT.TA: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:13:54,458 - INFO - [Thread-2 (refresh_worker)] - Caching data for key: defense_stocks
2025-06-21 03:13:54,459 - INFO - [Thread-2 (refresh_worker)] - Cache hit for key: defense_etfs
2025-06-21 03:13:54,459 - INFO - [Thread-2 (refresh_worker)] - Cache hit for key: conflict_data
2025-06-21 03:13:54,459 - INFO - [Thread-2 (refresh_worker)] - Cache hit for key: arms_trade
2025-06-21 03:14:41,383 - ERROR - [Thread-3 (analysis_worker)] - Error generating defense analysis: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-06-21 03:14:41,617 - ERROR - [Thread-6 (analysis_worker)] - Error generating defense analysis: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-06-21 03:15:20,913 - INFO - [MainThread] - Defense application closing...
