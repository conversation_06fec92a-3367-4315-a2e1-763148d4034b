2025-06-21 03:48:45,409 - INFO - [MainThread] - TiT Defense App 1.0.1 Starting...
2025-06-21 03:48:45,409 - INFO - [MainThread] - Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
2025-06-21 03:48:47,416 - INFO - [MainThread] - TiT Defense App main initialization started...
2025-06-21 03:48:47,416 - INFO - [MainThread] - DefenseCacheService initialized.
2025-06-21 03:48:47,417 - INFO - [MainThread] - DefenseDataService initialized with comprehensive defense coverage.
2025-06-21 03:48:47,418 - INFO - [MainThread] - DefenseAIService initialized with Gemini Pro.
2025-06-21 03:48:48,403 - INFO - [MainThread] - Defense app UI setup complete
2025-06-21 03:48:48,405 - INFO - [MainThread] - TiT Defense App 1.0.1 initialized successfully.
2025-06-21 03:48:48,920 - INFO - [Thread-1 (refresh_worker)] - Fetching defense stocks data for all regions...
2025-06-21 03:48:50,371 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:48:52,353 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:48:54,324 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:48:55,594 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:48:58,446 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:48:59,197 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:00,052 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:00,701 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:02,139 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:03,046 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:03,583 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:04,286 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:04,983 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:05,731 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:06,016 - ERROR - [Thread-1 (refresh_worker)] - $BAE.L: possibly delisted; no price data found  (period=1d)
2025-06-21 03:49:06,388 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:07,066 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:07,908 - ERROR - [Thread-1 (refresh_worker)] - $MEGG.L: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:49:08,277 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:09,052 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:09,751 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:10,523 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:10,868 - ERROR - [Thread-1 (refresh_worker)] - $THA.PA: possibly delisted; no price data found  (period=1d)
2025-06-21 03:49:11,271 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:12,070 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:12,787 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:13,547 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:14,988 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:15,273 - ERROR - [Thread-1 (refresh_worker)] - $FNC.MI: possibly delisted; no price data found  (period=1d)
2025-06-21 03:49:15,652 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
