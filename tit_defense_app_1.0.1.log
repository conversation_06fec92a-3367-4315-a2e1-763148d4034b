2025-06-21 03:02:41,498 - INFO - [MainThread] - TiT Defense App 1.0.1 Starting...
2025-06-21 03:02:41,499 - INFO - [MainThread] - Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
2025-06-21 03:02:43,568 - INFO - [MainThread] - TiT Defense App main initialization started...
2025-06-21 03:02:43,569 - INFO - [MainThread] - DefenseCacheService initialized.
2025-06-21 03:02:43,569 - INFO - [MainThread] - DefenseDataService initialized with comprehensive defense coverage.
2025-06-21 03:02:43,570 - INFO - [MainThread] - DefenseAIService initialized with Gemini Pro.
2025-06-21 03:02:44,648 - INFO - [MainThread] - Defense app UI setup complete
2025-06-21 03:02:44,648 - INFO - [MainThread] - TiT Defense App 1.0.1 initialized successfully.
2025-06-21 03:02:44,775 - INFO - [Thread-1 (refresh_worker)] - Fetching defense stocks data for all regions...
2025-06-21 03:02:45,778 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:02:46,868 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:02:47,589 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:02:48,370 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:02:49,726 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:02:50,401 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:02:51,078 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:02:51,686 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:02:52,173 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:02:52,769 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:02:53,480 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:02:54,782 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:02:55,334 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:02:55,815 - INFO - [Thread-2 (refresh_worker)] - Fetching defense stocks data for all regions...
2025-06-21 03:02:55,874 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:02:56,571 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:02:57,049 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:02:57,130 - INFO - [Thread-3 (refresh_worker)] - Fetching defense stocks data for all regions...
2025-06-21 03:02:57,202 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:02:57,625 - ERROR - [Thread-3 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:02:57,851 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:02:57,851 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:02:58,536 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:02:58,540 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:02:59,102 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:02:59,733 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:02:59,737 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:00,519 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:01,046 - ERROR - [Thread-3 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:01,440 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:01,881 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:02,032 - ERROR - [Thread-3 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:02,053 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:02,499 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:02,915 - ERROR - [Thread-3 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:03,183 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:03,362 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:03,611 - ERROR - [Thread-3 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:03,946 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:04,116 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:04,283 - ERROR - [Thread-3 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:04,766 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:04,769 - ERROR - [Thread-3 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:04,945 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:05,295 - ERROR - [Thread-1 (refresh_worker)] - $BAE.L: possibly delisted; no price data found  (period=1d)
2025-06-21 03:03:05,437 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:05,653 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:06,052 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:06,469 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 404: 
2025-06-21 03:03:06,482 - ERROR - [Thread-3 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:06,784 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:07,290 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:07,505 - ERROR - [Thread-1 (refresh_worker)] - $MEGG.L: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:03:07,947 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:08,407 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:08,890 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:09,427 - ERROR - [Thread-3 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:09,607 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:10,396 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:10,733 - ERROR - [Thread-1 (refresh_worker)] - $THA.PA: possibly delisted; no price data found  (period=1d)
2025-06-21 03:03:11,146 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:12,576 - ERROR - [Thread-2 (refresh_worker)] - $BAE.L: possibly delisted; no price data found  (period=1d)
2025-06-21 03:03:12,785 - ERROR - [Thread-3 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:12,921 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:13,434 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:13,441 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:13,570 - ERROR - [Thread-3 (refresh_worker)] - $BAE.L: possibly delisted; no price data found  (period=1d)
2025-06-21 03:03:13,778 - ERROR - [Thread-2 (refresh_worker)] - $MEGG.L: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:03:14,105 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:14,411 - ERROR - [Thread-3 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:14,814 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:14,858 - ERROR - [Thread-3 (refresh_worker)] - $MEGG.L: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:03:15,330 - ERROR - [Thread-1 (refresh_worker)] - $FNC.MI: possibly delisted; no price data found  (period=1d)
2025-06-21 03:03:15,414 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:15,587 - ERROR - [Thread-2 (refresh_worker)] - $THA.PA: possibly delisted; no price data found  (period=1d)
2025-06-21 03:03:15,742 - ERROR - [Thread-3 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:15,993 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:16,641 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:16,983 - ERROR - [Thread-3 (refresh_worker)] - $THA.PA: possibly delisted; no price data found  (period=1d)
2025-06-21 03:03:17,294 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:17,954 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:18,973 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:18,975 - ERROR - [Thread-3 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:19,184 - ERROR - [Thread-2 (refresh_worker)] - $FNC.MI: possibly delisted; no price data found  (period=1d)
2025-06-21 03:03:19,452 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:19,972 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:19,987 - ERROR - [Thread-3 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:20,125 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:20,283 - ERROR - [Thread-3 (refresh_worker)] - $FNC.MI: possibly delisted; no price data found  (period=1d)
2025-06-21 03:03:20,589 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:20,768 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:21,226 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:22,026 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:22,453 - ERROR - [Thread-3 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:23,840 - ERROR - [Thread-3 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:24,188 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:24,352 - ERROR - [Thread-3 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:24,986 - ERROR - [Thread-3 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:25,181 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:25,274 - ERROR - [Thread-1 (refresh_worker)] - $UNAC.ME: possibly delisted; no price data found  (period=1d)
2025-06-21 03:03:25,539 - ERROR - [Thread-3 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:25,722 - ERROR - [Thread-1 (refresh_worker)] - $AFKS.ME: possibly delisted; no price data found  (period=1d)
2025-06-21 03:03:25,736 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:25,898 - ERROR - [Thread-2 (refresh_worker)] - $UNAC.ME: possibly delisted; no price data found  (period=1d)
2025-06-21 03:03:26,085 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:26,486 - ERROR - [Thread-2 (refresh_worker)] - $AFKS.ME: possibly delisted; no price data found  (period=1d)
2025-06-21 03:03:26,938 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:27,157 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:27,379 - ERROR - [Thread-3 (refresh_worker)] - $UNAC.ME: possibly delisted; no price data found  (period=1d)
2025-06-21 03:03:27,606 - ERROR - [Thread-1 (refresh_worker)] - $600893.SZ: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:03:27,807 - ERROR - [Thread-3 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:27,936 - ERROR - [Thread-2 (refresh_worker)] - $600893.SZ: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:03:27,958 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:28,079 - ERROR - [Thread-3 (refresh_worker)] - $AFKS.ME: possibly delisted; no price data found  (period=1d)
2025-06-21 03:03:28,676 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:29,130 - ERROR - [Thread-3 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:29,333 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:29,348 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:29,546 - ERROR - [Thread-3 (refresh_worker)] - $600893.SZ: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:03:29,881 - ERROR - [Thread-3 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:30,061 - ERROR - [Thread-1 (refresh_worker)] - $ELBT.TA: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:03:30,065 - INFO - [Thread-1 (refresh_worker)] - Caching data for key: defense_stocks
2025-06-21 03:03:30,070 - INFO - [Thread-1 (refresh_worker)] - Fetching defense sector ETFs data...
2025-06-21 03:03:30,072 - ERROR - [Thread-2 (refresh_worker)] - $ELBT.TA: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:03:30,112 - INFO - [Thread-2 (refresh_worker)] - Caching data for key: defense_stocks
2025-06-21 03:03:30,113 - INFO - [Thread-2 (refresh_worker)] - Fetching defense sector ETFs data...
2025-06-21 03:03:30,460 - ERROR - [Thread-3 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:30,474 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:30,475 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:31,037 - ERROR - [Thread-3 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:31,340 - ERROR - [Thread-3 (refresh_worker)] - $ELBT.TA: possibly delisted; no price data found  (period=1d) (Yahoo error = "No data found, symbol may be delisted")
2025-06-21 03:03:31,341 - INFO - [Thread-3 (refresh_worker)] - Caching data for key: defense_stocks
2025-06-21 03:03:31,341 - INFO - [Thread-3 (refresh_worker)] - Fetching defense sector ETFs data...
2025-06-21 03:03:31,778 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:32,171 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:32,723 - ERROR - [Thread-3 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:33,672 - ERROR - [Thread-3 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:33,906 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:34,606 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:34,928 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:34,959 - INFO - [Thread-1 (refresh_worker)] - Caching data for key: defense_etfs
2025-06-21 03:03:34,960 - INFO - [Thread-1 (refresh_worker)] - Fetching conflict status and geopolitical data...
2025-06-21 03:03:34,960 - INFO - [Thread-1 (refresh_worker)] - Caching data for key: conflict_data
2025-06-21 03:03:34,961 - INFO - [Thread-1 (refresh_worker)] - Fetching arms trade data...
2025-06-21 03:03:34,961 - INFO - [Thread-1 (refresh_worker)] - Caching data for key: arms_trade
2025-06-21 03:03:35,344 - INFO - [Thread-3 (refresh_worker)] - Caching data for key: defense_etfs
2025-06-21 03:03:35,345 - INFO - [Thread-3 (refresh_worker)] - Cache hit for key: conflict_data
2025-06-21 03:03:35,346 - INFO - [Thread-3 (refresh_worker)] - Cache hit for key: arms_trade
2025-06-21 03:03:35,628 - ERROR - [Thread-2 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:03:35,836 - INFO - [Thread-2 (refresh_worker)] - Caching data for key: defense_etfs
2025-06-21 03:03:35,836 - INFO - [Thread-2 (refresh_worker)] - Cache hit for key: conflict_data
2025-06-21 03:03:35,837 - INFO - [Thread-2 (refresh_worker)] - Cache hit for key: arms_trade
2025-06-21 03:05:34,324 - ERROR - [Thread-4 (analysis_worker)] - Error generating defense analysis: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
