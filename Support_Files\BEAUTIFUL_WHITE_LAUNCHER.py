#!/usr/bin/env python3
"""
BEAUTIFUL WHITE THEME TiT LAUNCHER
7 SEPARATE APPS with GREEN READY DOTS
Created by <PERSON><PERSON>
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import subprocess
import sys
import os
import threading
from datetime import datetime

class BeautifulWhiteLauncher:
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.create_beautiful_interface()
        
    def setup_window(self):
        """Setup main window with beautiful white theme"""
        self.root.title("🚀 TiT Suite 1.0.1 - Beautiful White Launcher")
        self.root.geometry("1200x800")
        self.root.configure(bg='#FFFFFF')
        
        # Center window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1200 // 2)
        y = (self.root.winfo_screenheight() // 2) - (800 // 2)
        self.root.geometry(f"1200x800+{x}+{y}")
        
        # Make resizable
        self.root.resizable(True, True)
        
    def create_beautiful_interface(self):
        """Create beautiful white-themed interface"""
        # Main container with white background
        main_container = tk.Frame(self.root, bg='#FFFFFF')
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Header section
        self.create_header(main_container)
        
        # Apps grid section
        self.create_apps_grid(main_container)
        
        # Footer section
        self.create_footer(main_container)
        
    def create_header(self, parent):
        """Create beautiful header section"""
        header_frame = tk.Frame(parent, bg='#FFFFFF')
        header_frame.pack(fill=tk.X, pady=(0, 30))
        
        # Title
        title_label = tk.Label(
            header_frame,
            text="🚀 TiT Suite 1.0.1",
            font=("Segoe UI", 28, "bold"),
            fg='#2C3E50',
            bg='#FFFFFF'
        )
        title_label.pack()
        
        # Subtitle
        subtitle_label = tk.Label(
            header_frame,
            text="7 Professional Trading Intelligence Applications",
            font=("Segoe UI", 14),
            fg='#7F8C8D',
            bg='#FFFFFF'
        )
        subtitle_label.pack(pady=(5, 0))
        
        # Status with green dot
        status_frame = tk.Frame(header_frame, bg='#FFFFFF')
        status_frame.pack(pady=(10, 0))
        
        status_dot = tk.Label(
            status_frame,
            text="●",
            font=("Segoe UI", 16),
            fg='#27AE60',
            bg='#FFFFFF'
        )
        status_dot.pack(side=tk.LEFT)
        
        status_label = tk.Label(
            status_frame,
            text="All Systems Ready",
            font=("Segoe UI", 12),
            fg='#27AE60',
            bg='#FFFFFF'
        )
        status_label.pack(side=tk.LEFT, padx=(5, 0))
        
    def create_apps_grid(self, parent):
        """Create beautiful apps grid with scrolling"""
        # Apps container with scrolling
        canvas = tk.Canvas(parent, bg='#FFFFFF', highlightthickness=0)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='#FFFFFF')
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Define applications
        self.applications = [
            {
                'name': 'TiT Crypto App',
                'description': 'Cryptocurrency Trading Intelligence Suite',
                'file': 'Teu 1.0.1 MAin dancer.py',
                'icon': '₿',
                'color': '#F39C12',
                'features': ['Real-time prices', 'AI analysis', 'Portfolio tracking', 'News integration']
            },
            {
                'name': 'TiT Stock App',
                'description': 'Global Stock Market Intelligence Platform',
                'file': 'TiT_Stock_App_1.0.1.py',
                'icon': '📈',
                'color': '#27AE60',
                'features': ['Multi-country stocks', 'Technical analysis', 'News alerts', 'Portfolio management']
            },
            {
                'name': 'TiT Oil App',
                'description': 'Energy & Oil Market Intelligence Center',
                'file': 'TiT_Oil_App_1.0.1.py',
                'icon': '🛢️',
                'color': '#8E44AD',
                'features': ['Oil prices', 'Energy companies', 'Market analysis', 'Geopolitical impact']
            },
            {
                'name': 'TiT Gold App',
                'description': 'Precious Metals Trading Intelligence',
                'file': 'TiT_Gold_App_1.0.1.py',
                'icon': '🥇',
                'color': '#D4AF37',
                'features': ['Gold prices', 'Silver tracking', 'Mining stocks', 'Safe haven analysis']
            },
            {
                'name': 'TiT Health App',
                'description': 'Healthcare & Biotech Intelligence Suite',
                'file': 'TiT_Health_App_1.0.1.py',
                'icon': '🏥',
                'color': '#E74C3C',
                'features': ['Biotech stocks', 'Drug approvals', 'Healthcare trends', 'Medical innovation']
            },
            {
                'name': 'TiT Defense App',
                'description': 'Defense & War Trade Analysis Platform',
                'file': 'TiT_Defense_App_1.0.1.py',
                'icon': '🛡️',
                'color': '#34495E',
                'features': ['Defense stocks', 'Geopolitical analysis', 'War impact', 'Trade tensions']
            },
            {
                'name': 'TiT Science App',
                'description': 'Science, Technology & Space Intelligence Suite',
                'file': 'TiT_Science_App_1.0.1.py',
                'icon': '🚀',
                'color': '#4169E1',
                'features': ['Tech companies', 'Space exploration', 'AI developments', 'Innovation tracking']
            }
        ]
        
        # Create app cards in 3x3 grid
        row, col = 0, 0
        for app in self.applications:
            self.create_beautiful_app_card(scrollable_frame, app, row, col)

            col += 1
            if col >= 3:
                col = 0
                row += 1
    
    def create_beautiful_app_card(self, parent, app, row, col):
        """Create beautiful app card with white theme and green dots"""
        # Card frame with white background and border
        card_frame = tk.Frame(
            parent,
            bg='#FFFFFF',
            relief=tk.RAISED,
            bd=2,
            highlightbackground='#BDC3C7',
            highlightthickness=1
        )
        card_frame.grid(row=row, column=col, padx=15, pady=15, sticky="nsew", ipadx=10, ipady=10)
        
        # Configure grid weights for responsiveness
        parent.grid_rowconfigure(row, weight=1)
        parent.grid_columnconfigure(col, weight=1)
        
        # App icon and status
        header_frame = tk.Frame(card_frame, bg='#FFFFFF')
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Icon
        icon_label = tk.Label(
            header_frame,
            text=app['icon'],
            font=("Segoe UI", 32),
            bg='#FFFFFF'
        )
        icon_label.pack(side=tk.LEFT)
        
        # Status dot (green for ready)
        status_frame = tk.Frame(header_frame, bg='#FFFFFF')
        status_frame.pack(side=tk.RIGHT)
        
        status_dot = tk.Label(
            status_frame,
            text="●",
            font=("Segoe UI", 12),
            fg='#27AE60',
            bg='#FFFFFF'
        )
        status_dot.pack()
        
        status_text = tk.Label(
            status_frame,
            text="Ready",
            font=("Segoe UI", 8),
            fg='#27AE60',
            bg='#FFFFFF'
        )
        status_text.pack()
        
        # App name
        name_label = tk.Label(
            card_frame,
            text=app['name'],
            font=("Segoe UI", 16, "bold"),
            fg='#2C3E50',
            bg='#FFFFFF'
        )
        name_label.pack(anchor=tk.W, padx=5)
        
        # App description
        desc_label = tk.Label(
            card_frame,
            text=app['description'],
            font=("Segoe UI", 10),
            fg='#7F8C8D',
            bg='#FFFFFF',
            wraplength=250,
            justify=tk.LEFT
        )
        desc_label.pack(anchor=tk.W, padx=5, pady=(5, 10))
        
        # Features list
        features_label = tk.Label(
            card_frame,
            text="Key Features:",
            font=("Segoe UI", 9, "bold"),
            fg='#34495E',
            bg='#FFFFFF'
        )
        features_label.pack(anchor=tk.W, padx=5)
        
        for feature in app['features']:
            feature_label = tk.Label(
                card_frame,
                text=f"• {feature}",
                font=("Segoe UI", 8),
                fg='#7F8C8D',
                bg='#FFFFFF'
            )
            feature_label.pack(anchor=tk.W, padx=8, pady=1)
        
        # Launch button
        button_frame = tk.Frame(card_frame, bg='#FFFFFF')
        button_frame.pack(fill=tk.X, padx=10, pady=(5, 10))

        launch_btn = tk.Button(
            button_frame,
            text=f"🚀 LAUNCH",
            font=("Segoe UI", 12, "bold"),
            bg='#3498DB',
            fg='white',
            relief=tk.RAISED,
            bd=2,
            cursor="hand2",
            command=lambda: self.launch_application(app)
        )
        launch_btn.pack(fill=tk.X, pady=(0, 5))
        
        # File status
        file_status_label = tk.Label(
            button_frame,
            text=f"📁 {app['file']}",
            font=("Segoe UI", 8),
            fg='#7F8C8D',
            bg='#FFFFFF'
        )
        file_status_label.pack()
        
    def create_footer(self, parent):
        """Create footer with action buttons"""
        footer_frame = tk.Frame(parent, bg='#FFFFFF')
        footer_frame.pack(fill=tk.X, pady=(30, 0))
        
        # Action buttons frame
        button_frame = tk.Frame(footer_frame, bg='#FFFFFF')
        button_frame.pack()
        
        # Launch All button
        launch_all_button = tk.Button(
            button_frame,
            text="🚀 Launch All Apps",
            command=self.launch_all_applications,
            font=("Segoe UI", 12, "bold"),
            bg="#3498DB",
            fg="white",
            relief="flat",
            padx=20,
            pady=10,
            cursor="hand2"
        )
        launch_all_button.pack(side=tk.LEFT, padx=10)
        
        # Vietnamese Language button
        vietnamese_button = tk.Button(
            button_frame,
            text="Tiếng Việt",
            command=self.open_vietnamese_section,
            font=("Segoe UI", 12, "bold"),
            bg="#28a745",
            fg="white",
            relief="flat",
            padx=20,
            pady=10,
            cursor="hand2"
        )
        vietnamese_button.pack(side=tk.RIGHT, padx=10)

        # Exit button
        exit_button = tk.Button(
            button_frame,
            text="Exit App",
            command=self.root.quit,
            font=("Segoe UI", 12, "bold"),
            bg="#dc3545",
            fg="white",
            relief="flat",
            padx=20,
            pady=10,
            cursor="hand2"
        )
        exit_button.pack(side=tk.RIGHT, padx=10)
        
        # Copyright
        copyright_label = tk.Label(
            footer_frame,
            text="© 2025 Nguyen Le Vinh Quang - TiT Suite Professional Trading Intelligence",
            font=("Segoe UI", 9),
            fg='#95A5A6',
            bg='#FFFFFF'
        )
        copyright_label.pack(pady=(20, 0))
    
    def launch_application(self, app):
        """Launch individual application"""
        try:
            app_file = app['file']
            
            # Check if file exists
            if not os.path.exists(app_file):
                messagebox.showerror(
                    "❌ File Not Found",
                    f"Application file '{app_file}' not found.\n\n"
                    f"📁 Please ensure all TiT apps are in the same directory."
                )
                return
            
            # Launch the app
            process = subprocess.Popen([sys.executable, app_file])
            
            messagebox.showinfo(
                "🚀 Launch Successful!",
                f"✅ {app['name']} is starting!\n\n"
                f"⚡ Application window will appear shortly\n"
                f"🎯 Process ID: {process.pid}"
            )
            
        except Exception as e:
            messagebox.showerror("❌ Launch Error", f"Failed to launch {app['name']}:\n\n{str(e)}")
    
    def launch_all_applications(self):
        """Launch all applications"""
        try:
            result = messagebox.askyesno(
                "Launch All Applications",
                "This will launch all 7 TiT applications.\n\nContinue?"
            )
            
            if not result:
                return
            
            launched = 0
            failed = []
            
            for app in self.applications:
                try:
                    if os.path.exists(app['file']):
                        subprocess.Popen([sys.executable, app['file']])
                        launched += 1
                    else:
                        failed.append(f"{app['name']} (file not found)")
                except Exception as e:
                    failed.append(f"{app['name']} (error: {str(e)})")
            
            message = f"✅ Successfully launched {launched} applications!"
            if failed:
                message += f"\n\n❌ Failed to launch:\n" + "\n".join(failed)
            
            messagebox.showinfo("Launch Results", message)
            
        except Exception as e:
            messagebox.showerror("Launch Error", f"Failed to launch applications: {str(e)}")
    
    def open_vietnamese_section(self):
        """Open Vietnamese language section"""
        try:
            result = messagebox.askyesno(
                "Tiếng Việt - Vietnamese Section",
                "Chào mừng bạn đến với phần Tiếng Việt!\n"
                "Welcome to the Vietnamese section!\n\n"
                "Bạn có muốn mở tất cả ứng dụng TiT bằng tiếng Việt không?\n"
                "Would you like to open all TiT apps in Vietnamese?"
            )
            
            if result:
                # Launch Vietnamese versions if they exist
                vietnamese_apps = [
                    'Tin.py',  # Vietnamese Stock app
                    'TiT_Stock_App_1.0.1.py',  # Fallback to English
                    'TiT_Oil_App_1.0.1.py',
                    'TiT_Gold_App_1.0.1.py',
                    'TiT_Health_App_1.0.1.py',
                    'TiT_Defense_App_1.0.1.py',
                    'TiT_Science_App_1.0.1.py',
                    'TiT_Crypto_App_1.0.1.py'
                ]
                
                launched = 0
                for app_file in vietnamese_apps:
                    try:
                        if os.path.exists(app_file):
                            subprocess.Popen([sys.executable, app_file])
                            launched += 1
                    except Exception as e:
                        print(f"Error launching {app_file}: {e}")
                
                messagebox.showinfo(
                    "Thành công! - Success!",
                    f"✅ Đã khởi chạy {launched} ứng dụng!\n"
                    f"✅ Successfully launched {launched} applications!\n\n"
                    f"🇻🇳 Cảm ơn bạn đã sử dụng TiT Suite!"
                )
            
        except Exception as e:
            messagebox.showerror("Lỗi - Error", f"Không thể mở phần tiếng Việt: {str(e)}")

def main():
    """Main function"""
    print("🚀 Starting BEAUTIFUL WHITE THEME TiT Launcher...")
    print("💎 7 SEPARATE APPS with GREEN READY DOTS")
    print("👨‍💻 Created by Anh Quang")

    root = tk.Tk()
    launcher = BeautifulWhiteLauncher(root)

    print("✅ Beautiful launcher ready!")
    root.mainloop()

if __name__ == "__main__":
    main()
