2025-06-21 03:49:31,606 - INFO - [MainThread] - TiT Science App 1.0.1 Starting...
2025-06-21 03:49:31,607 - INFO - [MainThread] - Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
2025-06-21 03:49:33,771 - INFO - [MainThread] - TiT Science App main initialization started...
2025-06-21 03:49:33,772 - INFO - [MainThread] - ScienceCacheService initialized.
2025-06-21 03:49:33,772 - INFO - [MainThread] - ScienceDataService initialized with comprehensive technology coverage.
2025-06-21 03:49:33,773 - INFO - [MainThread] - ScienceAIService initialized with Gemini Pro.
2025-06-21 03:49:34,746 - INFO - [MainThread] - Science app UI setup complete
2025-06-21 03:49:34,747 - INFO - [MainThread] - TiT Science App 1.0.1 initialized successfully.
2025-06-21 03:49:35,247 - INFO - [Thread-1 (refresh_worker)] - Fetching tech stocks data for all sectors...
2025-06-21 03:49:35,835 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:36,556 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:37,863 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:38,480 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:39,056 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:39,641 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:40,228 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:40,867 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:41,425 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:41,988 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:42,686 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:45,278 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:45,811 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:46,855 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:47,460 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:48,103 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:48,719 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:49,239 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:49,837 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:50,397 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:50,990 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:51,623 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:52,311 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:53,583 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:55,476 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:56,086 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:56,661 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:57,252 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:58,355 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:58,856 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:49:59,410 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:50:00,084 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:50:00,573 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:50:01,037 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
2025-06-21 03:50:01,528 - ERROR - [Thread-1 (refresh_worker)] - HTTP Error 401: 
