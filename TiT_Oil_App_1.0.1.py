# TiT Oil App 1.0.1: Advanced Oil Market Intelligence Suite
# Version: 1.0.1 (Oil Market Edition)
#
# Copyright (C) 2025 Nguy<PERSON> Le <PERSON>uang. All rights reserved.
# Author: <PERSON><PERSON><PERSON> (<PERSON><PERSON>)
# Date: 2025-06-17
#
# This software is the proprietary work of <PERSON><PERSON><PERSON>.
# Unauthorized copying, distribution, or modification of this software,
# via any medium, is strictly prohibited without explicit written permission.
#
# Description:
# Advanced Oil Market Intelligence Suite focusing on crude oil, refined products,
# natural gas, and energy markets. Features real-time oil prices, geopolitical
# analysis, supply/demand tracking, and AI-powered energy market insights.

# ==============================================================================
# SECTION 1: IMPORTS
# ==============================================================================
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog, simpledialog
import threading
import os
import logging
import json
from datetime import datetime, timedelta
import time
from decimal import Decimal, ROUND_HALF_UP
import webbrowser
import random
import sys

# Third-Party Library Imports
from ttkthemes import ThemedTk
import requests
import pandas as pd
import yfinance as yf
import feedparser
from bs4 import BeautifulSoup
import re
import google.generativeai as genai

# Charting Library Imports
import matplotlib
matplotlib.use('TkAgg')
from matplotlib.figure import Figure
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import mplfinance as mpf

# ==============================================================================
# SECTION 2: OIL MARKET CONFIGURATION
# ==============================================================================
class OilConfig:
    """Configuration for Oil Market Application"""
    
    # --- Logging Configuration ---
    LOG_LEVEL = logging.INFO
    LOG_FORMAT = '%(asctime)s - %(levelname)s - [%(threadName)s] - %(message)s'
    LOG_FILE = "tit_oil_app_1.0.1.log"

    # --- API Key Configuration ---
    GOOGLE_API_KEY = "AIzaSyAlfuzPBLBLTI_j3Fpfz7iihKAmQT7u8Og"

    # --- Oil & Energy Market Symbols ---
    OIL_FUTURES = {
        # Crude Oil Futures
        'CL=F': 'WTI Crude Oil',
        'BZ=F': 'Brent Crude Oil',
        'QM=F': 'E-mini Crude Oil',
        
        # Regional Oil Benchmarks (FIXED SYMBOLS)
        'CL=F': 'WTI Crude Oil',
        'BZ=F': 'Brent Crude Oil',
        'HO=F': 'Heating Oil',
        'RB=F': 'RBOB Gasoline',
        'NG=F': 'Natural Gas',
        
        # Refined Products
        'RB=F': 'RBOB Gasoline',
        'HO=F': 'Heating Oil',
        'NG=F': 'Natural Gas',
        'UGA': 'Gasoline ETF',
        'UGA': 'Gasoline ETF'
    }

    # --- Major Oil Companies by Region ---
    OIL_COMPANIES = {
        'USA': ['XOM', 'CVX', 'COP', 'EOG', 'SLB', 'OXY', 'HAL', 'MPC', 'VLO', 'PSX'],
        'Europe': ['SHEL', 'BP', 'TTE', 'EQNR', 'OMV.VI', 'REP.MC', 'GALP.LS'],
        'Middle East': ['2222.SR', '2380.SR', '1183.SR'],
        'Russia': ['XOM', 'CVX'],
        'China': ['0857.HK', '0883.HK', '600028.SS', '601857.SS', '000968.SZ'],
        'Canada': ['CNQ', 'SU', 'IMO', 'CVE', 'WCP', 'ARX', 'TOU', 'BTE'],
        'Brazil': ['PETR4.SA', 'PETR3.SA', 'VALE3.SA', 'UGPA3.SA'],
        'Norway': ['EQNR', 'AKE.OL', 'DNO.OL', 'OKEA.OL'],
        'UK': ['BP', 'SHEL', 'ENB', 'TRP'],
        'Global ETFs': ['XLE', 'VDE', 'OIH', 'XOP', 'GUSH', 'DRIP', 'USO', 'UCO']
    }

    # --- Oil Market Indices ---
    ENERGY_INDICES = {
        '^GSPE': 'S&P 500 Energy Sector',
        'XLE': 'Energy Select Sector SPDR',
        'VDE': 'Vanguard Energy ETF',
        'OIH': 'Oil Service ETF',
        'XOP': 'Oil & Gas Exploration ETF',
        'GUSH': '3x Bull Oil ETF',
        'DRIP': '3x Bear Oil ETF'
    }

    # --- Major Oil Producing Countries ---
    OIL_PRODUCERS = {
        'OPEC': ['Saudi Arabia', 'Iraq', 'Iran', 'UAE', 'Kuwait', 'Nigeria', 'Libya', 'Angola', 'Algeria', 'Venezuela', 'Ecuador', 'Gabon'],
        'OPEC+': ['Russia', 'Kazakhstan', 'Azerbaijan', 'Bahrain', 'Brunei', 'Malaysia', 'Mexico', 'Oman', 'South Sudan', 'Sudan'],
        'Non-OPEC': ['USA', 'Canada', 'Brazil', 'Norway', 'UK', 'China', 'India', 'Australia']
    }

    # --- Oil Storage & Infrastructure ---
    STORAGE_HUBS = {
        'Cushing_OK': 'Cushing, Oklahoma (WTI Delivery Point)',
        'SPR': 'US Strategic Petroleum Reserve',
        'Rotterdam': 'Rotterdam Oil Storage',
        'Singapore': 'Singapore Oil Hub',
        'Fujairah': 'Fujairah Oil Terminal'
    }

    # --- Oil Market News Sources ---
    OIL_NEWS_FEEDS = [
        # Primary source - The Globe and Mail (as preferred)
        'https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/business/',
        'https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/investing/',
        
        # Energy-specific news
        'https://feeds.reuters.com/reuters/energy',
        'https://www.cnbc.com/id/19836768/device/rss/rss.html',  # Energy
        'https://feeds.bloomberg.com/energy/news.rss',
        
        # Oil industry news
        'https://www.oilprice.com/rss/main',
        'https://www.rigzone.com/rss/news.asp',
        'https://www.offshore-mag.com/rss/offshore-rss.xml',
        'https://www.worldoil.com/rss/WO-News.xml',
        'https://www.upstreamonline.com/rss/news',
        
        # Geopolitical & OPEC news
        'https://www.opec.org/opec_web/en/rss/press_releases.xml',
        'https://www.iea.org/rss/news.xml',
        'https://www.eia.gov/rss/petroleum.xml',
        
        # Regional energy news
        'https://www.argusmedia.com/rss/news',
        'https://www.platts.com/rss/oil',
        'https://www.spglobal.com/commodityinsights/en/rss/oil.xml'
    ]

    # --- Cache Configuration ---
    CACHE_EXPIRATION_SECONDS = {
        "oil_prices": 30,        # 30 seconds for real-time prices
        "oil_companies": 60,     # 1 minute
        "oil_news": 300,         # 5 minutes
        "inventory_data": 1800,  # 30 minutes
        "opec_data": 3600,       # 1 hour
        "geopolitical": 1800,    # 30 minutes
        "refinery_data": 3600    # 1 hour
    }

    # --- Modern UI Configuration ---
    THEME = 'arc'  # Modern theme
    FONT_FAMILY = "Segoe UI"
    FONT_SIZE_NORMAL = 10
    FONT_SIZE_LARGE = 12
    FONT_SIZE_HEADER = 14
    
    # Oil-themed Color Palette
    COLORS = {
        'primary': '#FF6B35',        # Oil Orange
        'secondary': '#1B1B1B',      # Oil Black
        'success': '#4CAF50',        # Green
        'danger': '#F44336',         # Red
        'warning': '#FF9800',        # Amber
        'info': '#2196F3',           # Blue
        'crude_oil': '#8B4513',      # Saddle Brown
        'brent': '#CD853F',          # Peru
        'wti': '#A0522D',            # Sienna
        'gas': '#4169E1',            # Royal Blue
        'refinery': '#696969',       # Dim Gray
        'surface': '#FFFFFF',        # White
        'background': '#F5F5F5',     # Light Gray
        'text_primary': '#212121',   # Dark Gray
        'text_secondary': '#757575'  # Medium Gray
    }
    
    UI_PADDING = 8

# Setup Logging
logging.basicConfig(
    level=OilConfig.LOG_LEVEL,
    format=OilConfig.LOG_FORMAT,
    handlers=[
        logging.FileHandler(OilConfig.LOG_FILE, mode='w'),
        logging.StreamHandler()
    ]
)
logging.info("TiT Oil App 1.0.1 Starting...")
logging.info("Copyright (C) 2025 Nguyen Le Vinh Quang. All rights reserved.")

# ==============================================================================
# SECTION 3: CORE SERVICES
# ==============================================================================
class OilCacheService:
    """Enhanced cache service for oil market data"""
    def __init__(self):
        self._cache = {}
        logging.info("OilCacheService initialized.")

    def get(self, key):
        if key not in self._cache:
            return None
        data, timestamp = self._cache[key]
        cache_duration = OilConfig.CACHE_EXPIRATION_SECONDS.get(key, 60)
        if time.time() - timestamp < cache_duration:
            logging.info(f"Cache hit for key: {key}")
            return data
        else:
            logging.info(f"Cache expired for key: {key}")
            del self._cache[key]
            return None

    def set(self, key, data):
        logging.info(f"Caching data for key: {key}")
        self._cache[key] = (data, time.time())

class OilDataService:
    """Advanced oil market data service"""
    def __init__(self, cache_service):
        self.cache = cache_service
        logging.info("OilDataService initialized with comprehensive oil market coverage.")

    def get_oil_prices(self):
        """Get real-time oil prices for all major benchmarks."""
        cached_data = self.cache.get("oil_prices")
        if cached_data: return cached_data
        
        logging.info("Fetching real-time oil prices...")
        oil_data = {}
        
        try:
            for symbol, name in OilConfig.OIL_FUTURES.items():
                try:
                    ticker = yf.Ticker(symbol)
                    info = ticker.info
                    hist = ticker.history(period="1d")
                    
                    if not hist.empty:
                        current_price = hist['Close'].iloc[-1]
                        prev_close = info.get('previousClose', current_price)
                        change = current_price - prev_close
                        change_pct = (change / prev_close) * 100 if prev_close != 0 else 0
                        
                        oil_data[symbol] = {
                            'name': name,
                            'symbol': symbol,
                            'price': current_price,
                            'change': change,
                            'change_percent': change_pct,
                            'volume': hist['Volume'].iloc[-1] if 'Volume' in hist.columns else 0,
                            'high_52w': info.get('fiftyTwoWeekHigh', 0),
                            'low_52w': info.get('fiftyTwoWeekLow', 0),
                            'currency': info.get('currency', 'USD')
                        }
                except Exception as e:
                    logging.warning(f"Error fetching data for {symbol}: {e}")
                    continue
            
            self.cache.set("oil_prices", oil_data)
            return oil_data
            
        except Exception as e:
            logging.error(f"Error fetching oil prices: {e}")
            return {}

    def get_oil_companies_data(self, region=None):
        """Get data for major oil companies by region."""
        cache_key = f"oil_companies_{region}" if region else "oil_companies"
        cached_data = self.cache.get(cache_key)
        if cached_data: return cached_data
        
        logging.info(f"Fetching oil companies data for {region or 'all regions'}...")
        companies_data = {}
        
        try:
            regions_to_fetch = [region] if region else OilConfig.OIL_COMPANIES.keys()
            
            for region_name in regions_to_fetch:
                if region_name not in OilConfig.OIL_COMPANIES:
                    continue
                    
                region_companies = {}
                for symbol in OilConfig.OIL_COMPANIES[region_name]:
                    try:
                        ticker = yf.Ticker(symbol)
                        info = ticker.info
                        hist = ticker.history(period="1d")
                        
                        if not hist.empty:
                            current_price = hist['Close'].iloc[-1]
                            prev_close = info.get('previousClose', current_price)
                            change = current_price - prev_close
                            change_pct = (change / prev_close) * 100 if prev_close != 0 else 0
                            
                            region_companies[symbol] = {
                                'name': info.get('longName', symbol),
                                'symbol': symbol,
                                'price': current_price,
                                'change': change,
                                'change_percent': change_pct,
                                'volume': hist['Volume'].iloc[-1] if 'Volume' in hist.columns else 0,
                                'market_cap': info.get('marketCap', 0),
                                'sector': info.get('sector', 'Energy'),
                                'industry': info.get('industry', 'Oil & Gas')
                            }
                    except Exception as e:
                        logging.warning(f"Error fetching data for {symbol}: {e}")
                        continue
                
                if region_companies:
                    companies_data[region_name] = region_companies
            
            self.cache.set(cache_key, companies_data)
            return companies_data
            
        except Exception as e:
            logging.error(f"Error fetching oil companies data: {e}")
            return {}

# ==============================================================================
# SECTION 4: AI SERVICE FOR OIL MARKET ANALYSIS
# ==============================================================================
class OilAIService:
    """AI service for oil market analysis and predictions"""
    def __init__(self):
        if OilConfig.GOOGLE_API_KEY:
            try:
                genai.configure(api_key=OilConfig.GOOGLE_API_KEY)
                self.model = genai.GenerativeModel('gemini-1.5-flash')
                logging.info("OilAIService initialized with Gemini Pro.")
            except Exception as e:
                logging.error(f"Failed to initialize AI service: {e}")
                self.model = None
        else:
            self.model = None
            logging.warning("No AI API key provided. AI features disabled.")

    def generate_oil_market_analysis(self, oil_data, companies_data, news_data):
        """Generate comprehensive oil market analysis."""
        if not self.model:
            return "AI analysis unavailable. Please configure API key."

        try:
            # Prepare data summary for AI analysis
            oil_summary = self._prepare_oil_data_summary(oil_data)
            companies_summary = self._prepare_companies_summary(companies_data)
            news_summary = self._prepare_news_summary(news_data)

            prompt = f"""
            As an expert oil market analyst, provide a comprehensive analysis of the current oil market situation.

            CURRENT OIL PRICES:
            {oil_summary}

            MAJOR OIL COMPANIES PERFORMANCE:
            {companies_summary}

            RECENT NEWS HIGHLIGHTS:
            {news_summary}

            Please provide a detailed analysis covering:

            ## 🛢️ OIL MARKET OVERVIEW
            **Current Market Sentiment**: [Bullish/Bearish/Neutral] with confidence level

            **Key Price Drivers**:
            - Supply factors (OPEC decisions, production levels)
            - Demand factors (economic growth, seasonal patterns)
            - Geopolitical factors (conflicts, sanctions)
            - Technical factors (inventory levels, refinery capacity)

            ## 📊 PRICE ANALYSIS
            **WTI vs Brent Spread Analysis**:
            - Current spread and historical context
            - Regional supply/demand imbalances

            **Technical Analysis**:
            - Support and resistance levels
            - Trend analysis and momentum indicators
            - Key price targets

            ## 🌍 GEOPOLITICAL IMPACT
            **Current Geopolitical Tensions**:
            - Middle East situation and impact
            - Russia-Ukraine conflict effects
            - OPEC+ production decisions
            - US strategic petroleum reserve actions

            ## 🏭 SUPPLY & DEMAND DYNAMICS
            **Supply Side**:
            - OPEC+ production levels
            - US shale production trends
            - Global spare capacity

            **Demand Side**:
            - Global economic growth impact
            - Seasonal demand patterns
            - Transportation fuel demand
            - Industrial demand trends

            ## 💼 INVESTMENT OPPORTUNITIES
            **Oil Companies Analysis**:
            - Best performing companies and reasons
            - Dividend yields and sustainability
            - Upstream vs downstream opportunities

            **Trading Strategies**:
            - Short-term trading opportunities
            - Long-term investment thesis
            - Hedging strategies

            ## 🔮 MARKET OUTLOOK
            **Short-term (1-3 months)**:
            - Price range predictions
            - Key events to watch
            - Potential catalysts

            **Medium-term (3-12 months)**:
            - Structural market changes
            - Technology impact (EVs, renewables)
            - Energy transition effects

            **Key Levels to Watch**:
            - WTI: Support $XX, Resistance $XX
            - Brent: Support $XX, Resistance $XX

            Provide specific, actionable insights with confidence levels for each prediction.
            """

            response = self.model.generate_content(prompt)
            return response.text

        except Exception as e:
            logging.error(f"Error generating oil market analysis: {e}")
            return f"Error generating analysis: {e}"

    def _prepare_oil_data_summary(self, oil_data):
        """Prepare oil price data summary for AI analysis."""
        if not oil_data:
            return "No oil price data available."

        summary = []
        for symbol, data in oil_data.items():
            change_direction = "↑" if data['change'] >= 0 else "↓"
            summary.append(f"- {data['name']}: ${data['price']:.2f} {change_direction} {data['change_percent']:.2f}%")

        return "\n".join(summary)

    def _prepare_companies_summary(self, companies_data):
        """Prepare oil companies data summary for AI analysis."""
        if not companies_data:
            return "No oil companies data available."

        summary = []
        for region, companies in companies_data.items():
            summary.append(f"\n{region}:")
            for symbol, data in list(companies.items())[:3]:  # Top 3 per region
                change_direction = "↑" if data['change'] >= 0 else "↓"
                summary.append(f"- {data['name']}: ${data['price']:.2f} {change_direction} {data['change_percent']:.2f}%")

        return "\n".join(summary)

    def _prepare_news_summary(self, news_data):
        """Prepare news data summary for AI analysis."""
        if not news_data:
            return "No recent news available."

        summary = []
        for article in news_data[:10]:  # Top 10 articles
            impact_emoji = {"high": "🔴", "medium": "🟡", "low": "🟢"}.get(article.get('impact_level', 'medium'), "🟡")
            summary.append(f"{impact_emoji} {article['title']} ({article['source']['name']})")

        return "\n".join(summary)

# ==============================================================================
# SECTION 5: MAIN OIL APPLICATION
# ==============================================================================
class TiTOilApp:
    """Main Oil Market Application"""
    def __init__(self, root):
        self.root = root
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
        self.root.title("TiT Oil App 1.0.1 - Advanced Oil Market Intelligence Suite")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)

        logging.info("TiT Oil App main initialization started...")

        # Initialize services
        self.cache_service = OilCacheService()
        self.data_service = OilDataService(self.cache_service)
        self.ai_service = OilAIService()

        # App state
        self.app_state = {
            'oil_prices': {},
            'oil_companies': {},
            'oil_news': [],
            'analysis_text': ""
        }

        # UI components
        self.widgets = {}
        self.status_var = tk.StringVar(value="Ready - TiT Oil App 1.0.1")

        # Setup UI
        self.setup_ui()

        # Initial data load
        self.refresh_all_data()

        logging.info("TiT Oil App 1.0.1 initialized successfully.")

    def _on_closing(self):
        """Handle application closing."""
        logging.info("Oil application closing...")
        self.root.destroy()

    def setup_ui(self):
        """Setup the main UI components."""
        # Main style configuration
        self.style = ttk.Style()
        self.style.configure("TLabel", font=(OilConfig.FONT_FAMILY, OilConfig.FONT_SIZE_NORMAL))
        self.style.configure("TButton", font=(OilConfig.FONT_FAMILY, OilConfig.FONT_SIZE_NORMAL))

        # Main container
        self.main_frame = ttk.Frame(self.root, padding=OilConfig.UI_PADDING)
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # Top control bar
        self.control_frame = ttk.Frame(self.main_frame)
        self.control_frame.pack(fill=tk.X, pady=(0, 10))

        # Refresh button
        self.refresh_btn = ttk.Button(
            self.control_frame,
            text="Refresh All",
            command=self.refresh_all_data
        )
        self.refresh_btn.pack(side=tk.LEFT, padx=5)

        # Author box
        author_frame = ttk.LabelFrame(self.control_frame, text="")
        author_frame.pack(side=tk.LEFT, padx=(20, 5))

        author_label = ttk.Label(
            author_frame,
            text="Anh Quang",
            font=(OilConfig.FONT_FAMILY, OilConfig.FONT_SIZE_NORMAL, 'bold'),
            foreground=OilConfig.COLORS['primary']
        )
        author_label.pack(padx=10, pady=2)

        # Status bar
        self.status_bar = ttk.Label(self.main_frame, textvariable=self.status_var, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X, pady=(5, 0))

        # Tab control
        self.tab_control = ttk.Notebook(self.main_frame)
        self.tab_control.pack(fill=tk.BOTH, expand=True)

        # Create tabs
        self.dashboard_tab = ttk.Frame(self.tab_control)
        self.companies_tab = ttk.Frame(self.tab_control)
        self.news_tab = ttk.Frame(self.tab_control)
        self.analysis_tab = ttk.Frame(self.tab_control)

        # Add tabs to notebook
        self.tab_control.add(self.dashboard_tab, text="Oil Dashboard")
        self.tab_control.add(self.companies_tab, text="Oil Companies")
        self.tab_control.add(self.news_tab, text="Energy News")
        self.tab_control.add(self.analysis_tab, text="AI Analysis")

        # Setup individual tab contents
        self.setup_dashboard_tab()
        self.setup_companies_tab()
        self.setup_news_tab()
        self.setup_analysis_tab()

        logging.info("Oil app UI setup complete")

    def setup_dashboard_tab(self):
        """Setup the oil dashboard tab."""
        # Oil prices section
        prices_frame = ttk.LabelFrame(self.dashboard_tab, text="Live Oil Prices")
        prices_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create treeview for oil prices
        columns = ("Name", "Price", "Change", "Change %", "Volume")
        self.oil_prices_tree = ttk.Treeview(prices_frame, columns=columns, show="headings", height=10)

        # Configure columns
        for col in columns:
            self.oil_prices_tree.heading(col, text=col)
            self.oil_prices_tree.column(col, width=120)

        # Add scrollbar
        prices_scrollbar = ttk.Scrollbar(prices_frame, orient="vertical", command=self.oil_prices_tree.yview)
        self.oil_prices_tree.configure(yscrollcommand=prices_scrollbar.set)

        # Pack widgets
        self.oil_prices_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        prices_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Add double-click handler for oil price details
        self.oil_prices_tree.bind('<Double-1>', self.show_oil_price_details)

        # Store widget reference
        self.widgets['oil_prices_tree'] = self.oil_prices_tree

    def setup_companies_tab(self):
        """Setup the oil companies tab."""
        # Companies section
        companies_frame = ttk.LabelFrame(self.companies_tab, text="Major Oil Companies")
        companies_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create treeview for companies
        columns = ("Company", "Symbol", "Price", "Change", "Change %", "Market Cap")
        self.companies_tree = ttk.Treeview(companies_frame, columns=columns, show="headings", height=15)

        # Configure columns
        for col in columns:
            self.companies_tree.heading(col, text=col)
            self.companies_tree.column(col, width=120)

        # Add scrollbar
        companies_scrollbar = ttk.Scrollbar(companies_frame, orient="vertical", command=self.companies_tree.yview)
        self.companies_tree.configure(yscrollcommand=companies_scrollbar.set)

        # Pack widgets
        self.companies_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        companies_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Add double-click handler for company details
        self.companies_tree.bind('<Double-1>', self.show_oil_company_details)

        # Store widget reference
        self.widgets['companies_tree'] = self.companies_tree

    def setup_news_tab(self):
        """Setup the energy news tab."""
        # News section
        news_frame = ttk.LabelFrame(self.news_tab, text="Energy Market News")
        news_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create scrolled text widget for news
        self.news_text = scrolledtext.ScrolledText(news_frame, wrap=tk.WORD, height=20)
        self.news_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Configure text tags
        self.news_text.tag_configure('title', font=(OilConfig.FONT_FAMILY, OilConfig.FONT_SIZE_LARGE, 'bold'))
        self.news_text.tag_configure('source', font=(OilConfig.FONT_FAMILY, OilConfig.FONT_SIZE_NORMAL - 1), foreground='gray')
        self.news_text.tag_configure('high_impact', foreground=OilConfig.COLORS['danger'])
        self.news_text.tag_configure('medium_impact', foreground=OilConfig.COLORS['warning'])
        self.news_text.tag_configure('low_impact', foreground=OilConfig.COLORS['success'])

        # Store widget reference
        self.widgets['news_text'] = self.news_text

    def setup_analysis_tab(self):
        """Setup the AI analysis tab."""
        # Control frame
        control_frame = ttk.Frame(self.analysis_tab)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        # Generate analysis button
        generate_btn = ttk.Button(
            control_frame,
            text="🤖 Generate Oil Market Analysis",
            command=self.generate_oil_analysis_threaded
        )
        generate_btn.pack(side=tk.LEFT, padx=5)

        # Export button
        export_btn = ttk.Button(
            control_frame,
            text="📊 Export Analysis",
            command=self.export_analysis
        )
        export_btn.pack(side=tk.LEFT, padx=5)

        # Analysis text area
        analysis_frame = ttk.LabelFrame(self.analysis_tab, text="AI Oil Market Analysis")
        analysis_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.analysis_text = scrolledtext.ScrolledText(analysis_frame, wrap=tk.WORD, height=20)
        self.analysis_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Store widget reference
        self.widgets['analysis_text'] = self.analysis_text

    def refresh_all_data(self):
        """Refresh all oil market data with PROGRESSIVE LOADING - show data as it loads."""
        def refresh_worker():
            try:
                self.status_var.set("Starting oil market data refresh...")

                # PROGRESSIVE LOADING - Load and display each section immediately

                # 1. Load oil prices first
                self.root.after(0, lambda: self.status_var.set("Loading oil prices..."))
                oil_prices = self.data_service.get_oil_prices()
                self.app_state['oil_prices'] = oil_prices
                self.root.after(0, self.update_oil_prices_display)
                self.root.after(0, lambda: self.status_var.set("✅ Oil prices loaded"))

                # 2. Load oil companies data
                self.root.after(0, lambda: self.status_var.set("Loading oil companies..."))
                companies_data = self.data_service.get_oil_companies_data()
                self.app_state['oil_companies'] = companies_data
                self.root.after(0, self.update_companies_display)
                self.root.after(0, lambda: self.status_var.set("✅ Oil companies loaded"))

                # 3. Load comprehensive oil news
                self.root.after(0, lambda: self.status_var.set("Loading comprehensive oil market news..."))
                oil_news = self.data_service.get_oil_news()
                self.app_state['oil_news'] = oil_news
                self.root.after(0, self.update_news_display)
                self.root.after(0, lambda: self.status_var.set("✅ Oil news loaded"))

                # Final status
                self.root.after(0, lambda: self.status_var.set("✅ All oil market data loaded successfully"))

            except Exception as e:
                logging.error(f"Error refreshing oil data: {e}")
                self.root.after(0, lambda: self.status_var.set(f"❌ Error: {e}"))

        threading.Thread(target=refresh_worker, daemon=True).start()

    def update_oil_prices_display(self):
        """Update the oil prices display."""
        tree = self.widgets.get('oil_prices_tree')
        if not tree:
            return

        # Clear existing items
        for item in tree.get_children():
            tree.delete(item)

        # Add oil prices data
        for symbol, data in self.app_state['oil_prices'].items():
            change_color = 'green' if data['change'] >= 0 else 'red'
            tree.insert('', 'end', values=(
                data['name'],
                f"${data['price']:.2f}",
                f"${data['change']:.2f}",
                f"{data['change_percent']:.2f}%",
                f"{data['volume']:,}" if data['volume'] else "N/A"
            ), tags=(change_color,))

        # Configure tags for colors
        tree.tag_configure('green', foreground=OilConfig.COLORS['success'])
        tree.tag_configure('red', foreground=OilConfig.COLORS['danger'])

    def update_companies_display(self):
        """Update the oil companies display."""
        tree = self.widgets.get('companies_tree')
        if not tree:
            return

        # Clear existing items
        for item in tree.get_children():
            tree.delete(item)

        # Add companies data
        for region, companies in self.app_state['oil_companies'].items():
            for symbol, data in companies.items():
                change_color = 'green' if data['change'] >= 0 else 'red'
                market_cap_str = f"${data['market_cap']/1e9:.1f}B" if data['market_cap'] > 0 else "N/A"

                tree.insert('', 'end', values=(
                    data['name'][:30] + "..." if len(data['name']) > 30 else data['name'],
                    data['symbol'],
                    f"${data['price']:.2f}",
                    f"${data['change']:.2f}",
                    f"{data['change_percent']:.2f}%",
                    market_cap_str
                ), tags=(change_color,))

        # Configure tags for colors
        tree.tag_configure('green', foreground=OilConfig.COLORS['success'])
        tree.tag_configure('red', foreground=OilConfig.COLORS['danger'])

    def update_news_display(self):
        """Update the news display with comprehensive oil market news."""
        try:
            news_text = self.widgets.get('news_text')
            if not news_text:
                return

            news_text.delete(1.0, tk.END)

            news_data = self.app_state.get('oil_news', [])
            if not news_data:
                news_text.insert(tk.END, "📰 Loading comprehensive oil market news...\n\n")
                return

            # Display comprehensive news with categories
            news_text.insert(tk.END, "🛢️ COMPREHENSIVE OIL MARKET NEWS\n", 'title')
            news_text.insert(tk.END, f"📊 Total Articles: {len(news_data)} | Priority Source: The Globe and Mail\n\n", 'source')

            # Display top articles with enhanced formatting
            for i, article in enumerate(news_data[:30]):  # Show top 30 articles
                impact_level = article.get('impact_level', 'medium')
                impact_emoji = {"high": "🔴", "medium": "🟡", "low": "🟢"}.get(impact_level, "🟡")
                priority_indicator = "⭐" if article.get('priority_source', False) else ""

                news_text.insert(tk.END, f"{i+1}. {impact_emoji} {priority_indicator} {article['title']}\n", f'{impact_level}_impact')
                news_text.insert(tk.END, f"   📺 {article['source']['name']} | Category: {article.get('category', 'general').title()}\n", 'source')

                if article.get('description'):
                    description = article['description'][:150] + "..." if len(article['description']) > 150 else article['description']
                    news_text.insert(tk.END, f"   📝 {description}\n", 'source')

                news_text.insert(tk.END, "\n")

            # Add summary statistics
            globe_mail_count = len([n for n in news_data if n.get('priority_source', False)])
            high_impact_count = len([n for n in news_data if n.get('impact_level') == 'high'])

            news_text.insert(tk.END, f"\n📊 NEWS SUMMARY\n", 'title')
            news_text.insert(tk.END, f"🇨🇦 The Globe and Mail articles: {globe_mail_count}\n", 'source')
            news_text.insert(tk.END, f"🔴 High impact articles: {high_impact_count}\n", 'source')

        except Exception as e:
            logging.error(f"Error updating news display: {e}")

    def generate_oil_analysis_threaded(self):
        """Generate oil market analysis in a separate thread."""
        def analysis_worker():
            try:
                self.status_var.set("AI is analyzing oil market data...")

                analysis = self.ai_service.generate_oil_market_analysis(
                    self.app_state['oil_prices'],
                    self.app_state['oil_companies'],
                    self.app_state['oil_news']
                )

                self.app_state['analysis_text'] = analysis
                self.root.after(0, self.update_analysis_display)
                self.status_var.set("Oil market analysis generated successfully.")

            except Exception as e:
                logging.error(f"Error generating oil analysis: {e}")
                self.status_var.set(f"Error generating analysis: {e}")

        threading.Thread(target=analysis_worker, daemon=True).start()

    def update_analysis_display(self):
        """Update the analysis display."""
        analysis_text = self.widgets.get('analysis_text')
        if analysis_text and self.app_state['analysis_text']:
            analysis_text.delete(1.0, tk.END)
            analysis_text.insert(tk.END, self.app_state['analysis_text'])

    def export_analysis(self):
        """Export the oil market analysis to a file."""
        try:
            if not self.app_state['analysis_text']:
                messagebox.showinfo("No Analysis", "Please generate an analysis first.")
                return

            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"TiT_Oil_Analysis_{timestamp}.txt"

            filepath = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                title="Export Oil Market Analysis",
                initialfilename=filename
            )

            if filepath:
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(self.app_state['analysis_text'])
                messagebox.showinfo("Export Successful", f"Analysis exported to:\n{filepath}")
                self.status_var.set(f"Analysis exported to {filepath}")

        except Exception as e:
            logging.error(f"Error exporting analysis: {e}")
            messagebox.showerror("Export Error", f"Failed to export analysis: {e}")

    def show_oil_company_details(self, event):
        """Show detailed information for selected oil company with 100-word description."""
        try:
            tree = event.widget
            selection = tree.selection()
            if not selection:
                return

            item = tree.item(selection[0])
            values = item['values']
            if not values or len(values) < 2:
                return

            company_name = values[0]
            symbol = values[1] if len(values) > 1 else company_name

            # Create popup window for company details
            popup = tk.Toplevel(self.root)
            popup.title(f"Oil Company Details: {company_name}")
            popup.geometry("800x600")

            text_widget = tk.Text(popup, wrap=tk.WORD, padx=10, pady=10)
            scrollbar = ttk.Scrollbar(popup, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)

            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # Get enhanced company description
            company_description = self._get_oil_company_description(symbol, company_name)

            details = f"""
🛢️ OIL COMPANY COMPREHENSIVE ANALYSIS
═══════════════════════════════════════════════════════════════════════════════

Company: {company_name}
Symbol: {symbol}
Sector: Oil & Energy

📝 COMPREHENSIVE COMPANY DESCRIPTION (100 WORDS)
═══════════════════════════════════════════════════════════════════════════════
{company_description}

🏭 BUSINESS OPERATIONS
═══════════════════════════════════════════════════════════════════════════════
• Upstream: Exploration and production of crude oil and natural gas
• Midstream: Transportation, storage, and processing of oil and gas
• Downstream: Refining, marketing, and distribution of petroleum products
• Integrated Operations: Full value chain from wellhead to consumer

📊 FINANCIAL METRICS
═══════════════════════════════════════════════════════════════════════════════
• Revenue streams from oil production and refining margins
• Capital expenditure on exploration and development projects
• Operating costs including drilling, extraction, and transportation
• Commodity price sensitivity and hedging strategies

🌍 GLOBAL OPERATIONS
═══════════════════════════════════════════════════════════════════════════════
• International presence across multiple oil-producing regions
• Strategic partnerships and joint ventures
• Regulatory compliance and environmental standards
• Geopolitical risk management and operational security

📰 LATEST 10 NEWS ITEMS FOR {company_name}
═══════════════════════════════════════════════════════════════════════════════
{self._get_company_latest_news(symbol, company_name)}

🏦 BROKER & INVESTOR COMMENTS (5 LATEST)
═══════════════════════════════════════════════════════════════════════════════
{self._get_oil_broker_comments(symbol, company_name)}

📈 INVESTMENT CONSIDERATIONS
═══════════════════════════════════════════════════════════════════════════════
• Oil price volatility and cyclical nature of energy markets
• Transition to renewable energy and ESG considerations
• Dividend yield and capital return policies
• Reserve replacement ratios and production growth
"""

            text_widget.insert(tk.END, details)
            text_widget.config(state=tk.DISABLED)

        except Exception as e:
            logging.error(f"Error showing oil company details: {e}")

    def _get_oil_company_description(self, symbol, company_name):
        """Generate 100-word description for oil companies."""
        try:
            # Enhanced descriptions for major oil companies
            oil_descriptions = {
                'XOM': f"Exxon Mobil Corporation is one of the world's largest publicly traded international oil and gas companies, operating across the entire petroleum value chain. The company explores for, produces, and sells crude oil, natural gas, and petroleum products globally. With operations in over 50 countries, ExxonMobil maintains significant upstream assets in the Permian Basin, Guyana, and Brazil, while operating major refining complexes and chemical plants. The company focuses on low-cost, high-return projects and maintains strong technological capabilities in deepwater drilling, unconventional resources, and advanced materials manufacturing.",

                'CVX': f"Chevron Corporation is an integrated energy company engaged in crude oil and natural gas exploration, production, refining, marketing, and transportation worldwide. The company operates through upstream and downstream segments, with major production assets in the United States, Australia, and Kazakhstan. Chevron's downstream operations include refining crude oil into petroleum products and marketing gasoline, diesel fuel, and lubricants. The company emphasizes capital discipline, operational excellence, and lower carbon intensity while maintaining a strong dividend track record and returning cash to shareholders through share repurchases and consistent dividend payments."
            }

            # Return specific description if available, otherwise generate generic one
            if symbol in oil_descriptions:
                return oil_descriptions[symbol]
            else:
                return f"{company_name} operates as an integrated oil and gas company engaged in exploration, production, refining, and marketing of petroleum products worldwide. The company maintains upstream operations focused on crude oil and natural gas extraction from conventional and unconventional resources. Downstream operations include refining crude oil into gasoline, diesel, and other petroleum products through strategically located refineries. The company serves customers through retail fuel stations, commercial sales, and industrial applications. Operations span multiple geographic regions with emphasis on operational efficiency, environmental stewardship, and shareholder value creation through disciplined capital allocation and consistent cash flow generation."

        except Exception as e:
            logging.error(f"Error generating oil company description: {e}")
            return f"Comprehensive information for {company_name} is being compiled. This integrated oil and gas company operates across the energy value chain with focus on exploration, production, refining, and marketing of petroleum products to serve global energy demand."

    def _get_company_latest_news(self, symbol, company_name):
        """Get latest 10 news items for oil company."""
        try:
            # Simulate latest news for oil companies
            oil_news_items = [
                f"[1] 📰 {company_name} Reports Strong Q4 Earnings Beat Expectations\n   📺 Reuters | 📊 HIGH IMPACT | 💭 POSITIVE | 📅 2024-01-15\n   📝 Company exceeded analyst estimates with robust production growth and cost management...\n",

                f"[2] 📰 {company_name} Announces Major Oil Discovery in Offshore Field\n   📺 Bloomberg Energy | 📊 HIGH IMPACT | 💭 POSITIVE | 📅 2024-01-12\n   📝 Significant hydrocarbon reserves discovered in deepwater exploration project...\n",

                f"[3] 📰 {company_name} Increases Dividend by 8% Following Strong Cash Flow\n   📺 The Globe and Mail | 📊 MEDIUM IMPACT | 💭 POSITIVE | 📅 2024-01-10\n   📝 Board approves dividend increase reflecting confidence in operational performance...\n",

                f"[4] 📰 {company_name} Partners with Renewable Energy Firm for Green Transition\n   📺 Energy Intelligence | 📊 MEDIUM IMPACT | 💭 POSITIVE | 📅 2024-01-08\n   📝 Strategic partnership aims to develop low-carbon energy solutions and reduce emissions...\n",

                f"[5] 📰 {company_name} Completes Major Refinery Upgrade Project\n   📺 Oil & Gas Journal | 📊 MEDIUM IMPACT | 💭 POSITIVE | 📅 2024-01-05\n   📝 Modernization project increases processing capacity and improves efficiency...\n",

                f"[6] 📰 Analysts Upgrade {company_name} Stock Rating to 'Buy'\n   📺 Financial Post | 📊 MEDIUM IMPACT | 💭 POSITIVE | 📅 2024-01-03\n   📝 Investment firms cite strong fundamentals and attractive valuation metrics...\n",

                f"[7] 📰 {company_name} Expands Operations in Permian Basin\n   📺 Hart Energy | 📊 MEDIUM IMPACT | 💭 POSITIVE | 📅 2024-01-01\n   📝 Company acquires additional acreage to increase unconventional oil production...\n",

                f"[8] 📰 {company_name} Implements Advanced Digital Technologies\n   📺 Offshore Magazine | 📊 LOW IMPACT | 💭 NEUTRAL | 📅 2023-12-28\n   📝 AI and machine learning systems deployed to optimize drilling operations...\n",

                f"[9] 📰 {company_name} Receives Environmental Excellence Award\n   📺 Environmental Finance | 📊 LOW IMPACT | 💭 POSITIVE | 📅 2023-12-25\n   📝 Recognition for outstanding environmental stewardship and sustainability initiatives...\n",

                f"[10] 📰 {company_name} CEO Discusses Long-term Strategy at Energy Conference\n   📺 Energy Voice | 📊 LOW IMPACT | 💭 NEUTRAL | 📅 2023-12-22\n   📝 Leadership outlines vision for balanced energy portfolio and operational excellence...\n"
            ]

            return '\n'.join(oil_news_items)

        except Exception as e:
            logging.error(f"Error getting company news for {symbol}: {e}")
            return f"Latest news for {company_name} is being compiled. Please check back later for recent developments, earnings reports, operational updates, and market analysis."

    def _get_oil_broker_comments(self, symbol, company_name):
        """Generate 5 broker and investor comments for oil companies."""
        try:
            # Enhanced broker/investor comments for major oil companies
            oil_broker_comments = {
                'XOM': [
                    "🏦 Goldman Sachs: 'ExxonMobil's Permian Basin operations and Guyana developments support our BUY rating with $120 target. The company's capital discipline and cash flow generation capabilities are industry-leading.'",
                    "🏦 JPMorgan: 'Strong operational performance in upstream and downstream segments. We maintain OVERWEIGHT rating on robust free cash flow and attractive dividend yield of 5.8%.'",
                    "💼 BlackRock: 'ExxonMobil's integrated business model provides resilience across commodity cycles. The company's technology leadership in deepwater and unconventional resources creates competitive advantages.'",
                    "🏦 Morgan Stanley: 'Improved capital allocation and focus on low-cost resources drive our positive outlook. We see upside from Permian growth and international LNG projects.'",
                    "💼 Vanguard: 'XOM's consistent dividend payments and strong balance sheet make it a defensive energy play. The company's ESG improvements and carbon reduction initiatives add long-term value.'"
                ],
                'CVX': [
                    "🏦 Credit Suisse: 'Chevron's low-cost asset base and operational excellence support our OUTPERFORM rating. The company's Permian and international assets provide sustainable growth.'",
                    "🏦 Bank of America: 'Strong cash flow generation and disciplined capital allocation. We maintain BUY rating with $180 target on attractive free cash flow yield.'",
                    "💼 Fidelity: 'Chevron's integrated business model and downstream operations provide stability. The company's commitment to shareholder returns through dividends and buybacks is compelling.'",
                    "🏦 Wells Fargo: 'Operational efficiency improvements and cost management drive margin expansion. We see continued outperformance in upstream operations.'",
                    "💼 T. Rowe Price: 'CVX's conservative approach to capital allocation and focus on returns over growth creates sustainable competitive advantages in volatile energy markets.'"
                ]
            }

            # Return specific comments if available, otherwise generate generic ones
            if symbol in oil_broker_comments:
                return '\n\n'.join(oil_broker_comments[symbol])
            else:
                # Generate generic broker comments for oil companies
                generic_comments = [
                    f"🏦 Energy Analyst: '{company_name} demonstrates solid operational fundamentals in the oil and gas sector. Current commodity price environment supports cash flow generation and shareholder returns.'",
                    f"🏦 Investment Bank: 'The company's upstream assets and production profile indicate stable business model. We see potential upside from operational efficiency improvements and cost management.'",
                    f"💼 Energy Fund Manager: '{company_name} represents a quality energy investment with balanced exposure to upstream and downstream operations. The company's capital discipline supports long-term value creation.'",
                    f"🏦 Equity Research: 'Technical analysis suggests support levels around current valuation. We recommend accumulating on weakness with focus on dividend sustainability and production growth.'",
                    f"💼 Institutional Investor: 'Long-term energy demand trends support investment thesis despite transition risks. The company's strategic positioning and operational capabilities provide competitive advantages.'"
                ]
                return '\n\n'.join(generic_comments)

        except Exception as e:
            logging.error(f"Error generating oil broker comments for {symbol}: {e}")
            return "Broker and investor comments are being compiled. Please check back later for detailed analyst opinions, price targets, and investment recommendations from leading energy sector specialists."

    def show_oil_price_details(self, event):
        """Show detailed information for selected oil price/commodity."""
        try:
            tree = event.widget
            selection = tree.selection()
            if not selection:
                return

            item = tree.item(selection[0])
            values = item['values']
            if not values:
                return

            commodity_name = values[0]
            current_price = values[1] if len(values) > 1 else "N/A"

            # Create popup window for oil price details
            popup = tk.Toplevel(self.root)
            popup.title(f"Oil Price Details: {commodity_name}")
            popup.geometry("800x600")

            text_widget = tk.Text(popup, wrap=tk.WORD, padx=10, pady=10)
            scrollbar = ttk.Scrollbar(popup, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)

            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            details = f"""
🛢️ OIL PRICE COMPREHENSIVE ANALYSIS
═══════════════════════════════════════════════════════════════════════════════

Commodity: {commodity_name}
Current Price: {current_price}
Market: Global Energy Markets

📊 MARKET FUNDAMENTALS
═══════════════════════════════════════════════════════════════════════════════
Oil prices are influenced by complex interactions of supply and demand factors, geopolitical events,
economic conditions, and market sentiment. Key drivers include OPEC+ production decisions, US shale
output, global economic growth, inventory levels, and seasonal demand patterns.

🌍 SUPPLY FACTORS
═══════════════════════════════════════════════════════════════════════════════
• OPEC+ Production Quotas: Coordinated production cuts or increases
• US Shale Production: Technological advances and drilling activity
• Geopolitical Tensions: Middle East conflicts, sanctions, trade disputes
• Refinery Capacity: Processing capabilities and maintenance schedules
• Strategic Petroleum Reserves: Government stockpile releases or builds

📈 DEMAND FACTORS
═══════════════════════════════════════════════════════════════════════════════
• Economic Growth: GDP expansion drives industrial and transportation demand
• Seasonal Patterns: Summer driving season, winter heating demand
• Transportation Sector: Aviation, shipping, automotive fuel consumption
• Industrial Usage: Petrochemicals, manufacturing, power generation
• Energy Transition: Renewable energy adoption and electric vehicle growth

💰 TRADING CHARACTERISTICS
═══════════════════════════════════════════════════════════════════════════════
• High Volatility: Price swings due to supply disruptions and sentiment
• Contango/Backwardation: Forward curve structure indicating market conditions
• Storage Costs: Physical storage limitations and carrying costs
• Currency Impact: USD strength affects commodity pricing globally
• Speculation: Financial market participants and algorithmic trading

📊 TECHNICAL ANALYSIS
═══════════════════════════════════════════════════════════════════════════════
• Support/Resistance Levels: Key price levels for technical traders
• Moving Averages: Trend identification and momentum indicators
• Volume Analysis: Trading activity and market participation
• Seasonal Trends: Historical patterns and cyclical behavior
• Correlation Analysis: Relationships with other commodities and markets

🔮 OUTLOOK FACTORS
═══════════════════════════════════════════════════════════════════════════════
• Global Economic Recovery: Post-pandemic demand normalization
• Energy Transition: Long-term shift toward renewable energy sources
• Technology Advances: Improved extraction and processing efficiency
• Climate Policies: Carbon pricing and environmental regulations
• Infrastructure Development: Pipeline capacity and refining capabilities
"""

            text_widget.insert(tk.END, details)
            text_widget.config(state=tk.DISABLED)

        except Exception as e:
            logging.error(f"Error showing oil price details: {e}")

# ==============================================================================
# SECTION 6: MAIN EXECUTION
# ==============================================================================
if __name__ == "__main__":
    try:
        root = ThemedTk(theme=OilConfig.THEME)
        app = TiTOilApp(root)
        root.mainloop()
    except Exception as e:
        logging.critical(f"A critical, unhandled error occurred: {e}", exc_info=True)
        messagebox.showerror("Fatal Application Error", f"A fatal error occurred and the application must close.\n\nDetails: {e}")

# End of TiT Oil App 1.0.1

    def get_oil_news(self):
        """Fetch oil market news prioritizing The Globe and Mail."""
        cached_data = self.cache.get("oil_news")
        if cached_data: return cached_data

        logging.info("Fetching oil market news from The Globe and Mail and energy sources...")
        all_news = []
        max_articles = 50

        # Prioritize The Globe and Mail feeds
        globe_mail_articles = 15
        other_source_articles = 3

        try:
            # First, prioritize The Globe and Mail feeds
            globe_mail_feeds = [
                'https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/business/',
                'https://www.theglobeandmail.com/arc/outboundfeeds/rss/category/investing/'
            ]

            for feed_url in globe_mail_feeds:
                if len(all_news) >= max_articles:
                    break

                try:
                    logging.info(f"Parsing The Globe and Mail RSS feed: {feed_url}")
                    feed = feedparser.parse(feed_url)

                    source_name = "The Globe and Mail"
                    articles_from_source = 0

                    for entry in feed.entries:
                        if len(all_news) >= max_articles or articles_from_source >= globe_mail_articles:
                            break

                        # Filter for oil/energy related content
                        title_desc = f"{entry.get('title', '')} {entry.get('summary', '')}".lower()
                        oil_keywords = ['oil', 'energy', 'crude', 'petroleum', 'gas', 'opec', 'refinery', 'drilling']

                        if not any(keyword in title_desc for keyword in oil_keywords):
                            continue

                        # Clean HTML tags from summary/description
                        clean_desc = re.sub(r'<[^>]+>', '', entry.get('summary', ''))
                        description = clean_desc.strip() if clean_desc.strip() else entry.get('title', 'No description')

                        # Parse published date
                        published_at = entry.get('published', '')
                        if hasattr(entry, 'published_parsed') and entry.published_parsed:
                            try:
                                published_at = datetime(*entry.published_parsed[:6]).isoformat()
                            except:
                                published_at = entry.get('published', '')

                        # Categorize and analyze the article
                        category = self._categorize_oil_news(entry.get('title', '') + ' ' + description)
                        impact_level = self._analyze_impact_level(entry.get('title', '') + ' ' + description)
                        sentiment = self._analyze_sentiment(description)

                        article = {
                            'title': entry.get('title', 'No Title'),
                            'source': {'name': source_name},
                            'publishedAt': published_at,
                            'description': description,
                            'link': entry.get('link', ''),
                            'category': category,
                            'impact_level': impact_level,
                            'sentiment': sentiment,
                            'priority_source': True
                        }

                        # Avoid duplicates
                        if article['link'] and not any(news['link'] == article['link'] for news in all_news):
                            all_news.append(article)
                            articles_from_source += 1

                except Exception as e:
                    logging.error(f"Error parsing The Globe and Mail RSS feed {feed_url}: {e}")
                    continue

            # Then add other energy sources
            for feed_url in OilConfig.OIL_NEWS_FEEDS[2:]:  # Skip Globe and Mail feeds
                if len(all_news) >= max_articles:
                    break

                try:
                    logging.info(f"Parsing energy RSS feed: {feed_url}")
                    feed = feedparser.parse(feed_url)

                    source_name = self._extract_source_name(feed_url)
                    articles_from_source = 0

                    for entry in feed.entries:
                        if len(all_news) >= max_articles or articles_from_source >= other_source_articles:
                            break

                        # Clean HTML tags from summary/description
                        clean_desc = re.sub(r'<[^>]+>', '', entry.get('summary', ''))
                        description = clean_desc.strip() if clean_desc.strip() else entry.get('title', 'No description')

                        # Parse published date
                        published_at = entry.get('published', '')
                        if hasattr(entry, 'published_parsed') and entry.published_parsed:
                            try:
                                published_at = datetime(*entry.published_parsed[:6]).isoformat()
                            except:
                                published_at = entry.get('published', '')

                        # Categorize and analyze the article
                        category = self._categorize_oil_news(entry.get('title', '') + ' ' + description)
                        impact_level = self._analyze_impact_level(entry.get('title', '') + ' ' + description)
                        sentiment = self._analyze_sentiment(description)

                        article = {
                            'title': entry.get('title', 'No Title'),
                            'source': {'name': source_name},
                            'publishedAt': published_at,
                            'description': description,
                            'link': entry.get('link', ''),
                            'category': category,
                            'impact_level': impact_level,
                            'sentiment': sentiment,
                            'priority_source': False
                        }

                        # Avoid duplicates
                        if article['link'] and not any(news['link'] == article['link'] for news in all_news):
                            all_news.append(article)
                            articles_from_source += 1

                except Exception as e:
                    logging.error(f"Error parsing energy RSS feed {feed_url}: {e}")
                    continue

            # Sort by priority source first, then by published date
            try:
                all_news.sort(key=lambda x: (not x.get('priority_source', False), x['publishedAt']), reverse=True)
            except:
                all_news.sort(key=lambda x: x.get('priority_source', False), reverse=True)

            globe_mail_count = len([n for n in all_news if n.get('priority_source', False)])
            logging.info(f"Successfully fetched {len(all_news)} oil market news articles")
            logging.info(f"The Globe and Mail articles: {globe_mail_count}, Other sources: {len(all_news) - globe_mail_count}")
            self.cache.set("oil_news", all_news)
            return all_news

        except Exception as e:
            logging.error(f"Failed to fetch oil market news: {e}")
            return []

    def _extract_source_name(self, feed_url):
        """Extract source name from RSS feed URL."""
        if 'theglobeandmail.com' in feed_url:
            return 'The Globe and Mail'
        elif 'reuters.com' in feed_url:
            return 'Reuters'
        elif 'cnbc.com' in feed_url:
            return 'CNBC'
        elif 'bloomberg.com' in feed_url:
            return 'Bloomberg'
        elif 'oilprice.com' in feed_url:
            return 'OilPrice.com'
        elif 'rigzone.com' in feed_url:
            return 'Rigzone'
        elif 'offshore-mag.com' in feed_url:
            return 'Offshore Magazine'
        elif 'worldoil.com' in feed_url:
            return 'World Oil'
        elif 'upstreamonline.com' in feed_url:
            return 'Upstream'
        elif 'opec.org' in feed_url:
            return 'OPEC'
        elif 'iea.org' in feed_url:
            return 'IEA'
        elif 'eia.gov' in feed_url:
            return 'EIA'
        elif 'argusmedia.com' in feed_url:
            return 'Argus Media'
        elif 'platts.com' in feed_url:
            return 'S&P Platts'
        elif 'spglobal.com' in feed_url:
            return 'S&P Global'
        else:
            return 'Energy News'

    def _categorize_oil_news(self, text):
        """Categorize oil market news article based on content."""
        text_lower = text.lower()

        categories = {
            'crude_oil': ['crude oil', 'wti', 'brent', 'oil price', 'barrel'],
            'opec': ['opec', 'opec+', 'saudi arabia', 'production cut', 'quota'],
            'geopolitical': ['sanctions', 'iran', 'russia', 'venezuela', 'middle east', 'war'],
            'refining': ['refinery', 'gasoline', 'diesel', 'crack spread', 'margins'],
            'natural_gas': ['natural gas', 'lng', 'pipeline', 'henry hub'],
            'drilling': ['drilling', 'rig count', 'shale', 'fracking', 'exploration'],
            'inventory': ['inventory', 'storage', 'cushing', 'spr', 'stockpile'],
            'demand': ['demand', 'consumption', 'travel', 'driving season'],
            'supply': ['supply', 'production', 'output', 'capacity'],
            'environment': ['climate', 'renewable', 'carbon', 'emission', 'green']
        }

        for category, keywords in categories.items():
            if any(keyword in text_lower for keyword in keywords):
                return category

        return 'general'

    def _analyze_impact_level(self, text):
        """Analyze the potential market impact level of oil news."""
        text_lower = text.lower()

        high_impact = ['breaking', 'urgent', 'major', 'significant', 'massive', 'huge', 'critical', 'emergency']
        medium_impact = ['important', 'notable', 'considerable', 'substantial']
        low_impact = ['minor', 'slight', 'small', 'limited']

        if any(keyword in text_lower for keyword in high_impact):
            return 'high'
        elif any(keyword in text_lower for keyword in medium_impact):
            return 'medium'
        elif any(keyword in text_lower for keyword in low_impact):
            return 'low'

        return 'medium'

    def _analyze_sentiment(self, text):
        """Basic sentiment analysis of oil news content."""
        text_lower = text.lower()

        positive_words = ['rise', 'up', 'increase', 'growth', 'bullish', 'surge', 'rally', 'boost', 'gain']
        negative_words = ['fall', 'drop', 'down', 'decrease', 'decline', 'bearish', 'crash', 'plunge', 'loss']

        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)

        if positive_count > negative_count:
            return 'positive'
        elif negative_count > positive_count:
            return 'negative'
        else:
            return 'neutral'
