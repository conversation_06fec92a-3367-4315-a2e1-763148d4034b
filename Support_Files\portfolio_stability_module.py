# Portfolio Stability Module for TiT Suite
# Advanced portfolio management with stability engines
# Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.

import json
import os
import time
import logging
import threading
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import shutil

class StablePortfolio:
    """Advanced portfolio management with stability and backup features"""
    
    def __init__(self, portfolio_file: str = "tit_portfolio.json"):
        self.portfolio_file = portfolio_file
        self.backup_dir = "portfolio_backups"
        self.portfolio_data = {}
        self.lock = threading.Lock()
        self.auto_backup_interval = 300  # 5 minutes
        self.last_backup_time = 0
        
        # Ensure backup directory exists
        os.makedirs(self.backup_dir, exist_ok=True)
        
        # Load existing portfolio
        self.load_portfolio()
        
        # Initialize with default structure if empty
        if not self.portfolio_data:
            self._initialize_default_portfolio()
        
        logging.info("✅ StablePortfolio initialized with backup system")
    
    def _initialize_default_portfolio(self):
        """Initialize default portfolio structure"""
        self.portfolio_data = {
            "version": "1.0.1",
            "created": datetime.now().isoformat(),
            "last_updated": datetime.now().isoformat(),
            "total_value": 0.0,
            "total_invested": 0.0,
            "total_profit_loss": 0.0,
            "holdings": {},
            "transactions": [],
            "settings": {
                "auto_backup": True,
                "risk_tolerance": "medium",
                "investment_strategy": "balanced"
            },
            "performance": {
                "daily_change": 0.0,
                "weekly_change": 0.0,
                "monthly_change": 0.0,
                "yearly_change": 0.0
            }
        }
        self.save_portfolio()
    
    def load_portfolio(self) -> bool:
        """Load portfolio from file with error recovery"""
        try:
            if os.path.exists(self.portfolio_file):
                with open(self.portfolio_file, 'r', encoding='utf-8') as f:
                    self.portfolio_data = json.load(f)
                logging.info(f"✅ Portfolio loaded from {self.portfolio_file}")
                return True
            else:
                logging.info("📁 No existing portfolio found, will create new one")
                return False
        except Exception as e:
            logging.error(f"❌ Failed to load portfolio: {e}")
            # Try to restore from backup
            return self._restore_from_backup()
    
    def save_portfolio(self) -> bool:
        """Save portfolio with atomic write and backup"""
        with self.lock:
            try:
                # Update timestamp
                self.portfolio_data["last_updated"] = datetime.now().isoformat()
                
                # Atomic write (write to temp file first)
                temp_file = f"{self.portfolio_file}.tmp"
                with open(temp_file, 'w', encoding='utf-8') as f:
                    json.dump(self.portfolio_data, f, indent=2, ensure_ascii=False)
                
                # Replace original file
                if os.path.exists(self.portfolio_file):
                    shutil.copy2(self.portfolio_file, f"{self.portfolio_file}.backup")
                
                shutil.move(temp_file, self.portfolio_file)
                
                # Auto backup if needed
                self._auto_backup_if_needed()
                
                logging.info("✅ Portfolio saved successfully")
                return True
                
            except Exception as e:
                logging.error(f"❌ Failed to save portfolio: {e}")
                return False
    
    def add_transaction(self, symbol: str, transaction_type: str, quantity: float, 
                       price: float, fees: float = 0.0, notes: str = "") -> bool:
        """Add transaction with validation and portfolio update"""
        try:
            transaction = {
                "id": f"txn_{int(time.time() * 1000)}",
                "timestamp": datetime.now().isoformat(),
                "symbol": symbol.upper(),
                "type": transaction_type.lower(),  # buy, sell
                "quantity": float(quantity),
                "price": float(price),
                "fees": float(fees),
                "total": float(quantity * price + fees),
                "notes": notes
            }
            
            # Validate transaction
            if transaction_type.lower() not in ['buy', 'sell']:
                raise ValueError("Transaction type must be 'buy' or 'sell'")
            
            if quantity <= 0 or price <= 0:
                raise ValueError("Quantity and price must be positive")
            
            # Add to transactions list
            self.portfolio_data["transactions"].append(transaction)
            
            # Update holdings
            self._update_holdings(transaction)
            
            # Save portfolio
            self.save_portfolio()
            
            logging.info(f"✅ Transaction added: {transaction_type} {quantity} {symbol} @ ${price}")
            return True
            
        except Exception as e:
            logging.error(f"❌ Failed to add transaction: {e}")
            return False
    
    def _update_holdings(self, transaction: Dict):
        """Update holdings based on transaction"""
        symbol = transaction["symbol"]
        
        if symbol not in self.portfolio_data["holdings"]:
            self.portfolio_data["holdings"][symbol] = {
                "quantity": 0.0,
                "average_price": 0.0,
                "total_invested": 0.0,
                "current_value": 0.0,
                "profit_loss": 0.0,
                "first_purchase": transaction["timestamp"]
            }
        
        holding = self.portfolio_data["holdings"][symbol]
        
        if transaction["type"] == "buy":
            # Calculate new average price
            total_cost = holding["total_invested"] + transaction["total"]
            total_quantity = holding["quantity"] + transaction["quantity"]
            
            holding["quantity"] = total_quantity
            holding["total_invested"] = total_cost
            holding["average_price"] = total_cost / total_quantity if total_quantity > 0 else 0
            
        elif transaction["type"] == "sell":
            # Reduce quantity
            holding["quantity"] -= transaction["quantity"]
            
            # If all sold, reset
            if holding["quantity"] <= 0:
                holding["quantity"] = 0
                holding["average_price"] = 0
                holding["total_invested"] = 0
    
    def get_portfolio_summary(self) -> Dict:
        """Get comprehensive portfolio summary"""
        return {
            "total_value": self.portfolio_data.get("total_value", 0),
            "total_invested": self.portfolio_data.get("total_invested", 0),
            "total_profit_loss": self.portfolio_data.get("total_profit_loss", 0),
            "holdings_count": len(self.portfolio_data.get("holdings", {})),
            "transactions_count": len(self.portfolio_data.get("transactions", [])),
            "last_updated": self.portfolio_data.get("last_updated", ""),
            "performance": self.portfolio_data.get("performance", {})
        }
    
    def _auto_backup_if_needed(self):
        """Create automatic backup if interval has passed"""
        current_time = time.time()
        if current_time - self.last_backup_time > self.auto_backup_interval:
            self._create_backup()
            self.last_backup_time = current_time
    
    def _create_backup(self):
        """Create timestamped backup"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = os.path.join(self.backup_dir, f"portfolio_backup_{timestamp}.json")
            shutil.copy2(self.portfolio_file, backup_file)
            
            # Keep only last 10 backups
            self._cleanup_old_backups()
            
            logging.info(f"✅ Portfolio backup created: {backup_file}")
        except Exception as e:
            logging.error(f"❌ Backup creation failed: {e}")
    
    def _cleanup_old_backups(self):
        """Keep only the 10 most recent backups"""
        try:
            backup_files = [f for f in os.listdir(self.backup_dir) if f.startswith("portfolio_backup_")]
            backup_files.sort(reverse=True)
            
            for old_backup in backup_files[10:]:
                os.remove(os.path.join(self.backup_dir, old_backup))
        except Exception as e:
            logging.error(f"❌ Backup cleanup failed: {e}")
    
    def _restore_from_backup(self) -> bool:
        """Restore portfolio from most recent backup"""
        try:
            backup_files = [f for f in os.listdir(self.backup_dir) if f.startswith("portfolio_backup_")]
            if not backup_files:
                return False
            
            latest_backup = max(backup_files)
            backup_path = os.path.join(self.backup_dir, latest_backup)
            
            with open(backup_path, 'r', encoding='utf-8') as f:
                self.portfolio_data = json.load(f)
            
            logging.info(f"✅ Portfolio restored from backup: {latest_backup}")
            return True
            
        except Exception as e:
            logging.error(f"❌ Backup restoration failed: {e}")
            return False

# Global instances
stable_portfolio = StablePortfolio()

# Convenience functions
def get_portfolio() -> Dict:
    """Get current portfolio data"""
    return stable_portfolio.portfolio_data

def add_transaction(symbol: str, transaction_type: str, quantity: float, 
                   price: float, fees: float = 0.0, notes: str = "") -> bool:
    """Add a new transaction"""
    return stable_portfolio.add_transaction(symbol, transaction_type, quantity, price, fees, notes)

def get_portfolio_summary() -> Dict:
    """Get portfolio summary"""
    return stable_portfolio.get_portfolio_summary()

def save_portfolio() -> bool:
    """Save portfolio to file"""
    return stable_portfolio.save_portfolio()

def load_portfolio() -> bool:
    """Load portfolio from file"""
    return stable_portfolio.load_portfolio()

logging.info("✅ Portfolio stability module loaded successfully!")
