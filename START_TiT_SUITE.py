# START TiT SUITE - <PERSON>IN LAUNCHER
# Version: 1.0.1 (COMPLETE RESTORATION)
#
# Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
# Author: <PERSON><PERSON><PERSON>
# Date: 2025-06-20
#
# RESTORED LAUNCHER WITH ALL 7 APPS VISIBLE AND API SETTINGS OUTSIDE MENU

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import subprocess
import sys
import os
import re

class TiTSuiteLauncher:
    def __init__(self, root):
        self.root = root
        self.root.title("🚀 TiT Financial Intelligence Suite 1.0.1 - Complete Launcher 🚀")
        self.root.geometry("1400x900")
        self.root.configure(bg='#F8FAFC')
        self.root.resizable(True, True)
        self.root.minsize(1200, 800)

        # Initialize language system
        self.current_language = "English"
        self.setup_translations()

        # Center window
        self.center_window()

        # Configure styling
        self.setup_styling()

        # Create main UI
        self.create_main_ui()

    def setup_translations(self):
        """Setup Vietnamese translations"""
        self.translations = {
            "English": {
                "title": "🚀 TiT FINANCIAL INTELLIGENCE SUITE 🚀",
                "subtitle": "💎 Market Intelligence & AI Analysis Platform 💎",
                "api_settings": "API Settings",
                "google_api_key": "Google API Key:",
                "save_settings": "Save Settings",
                "theme_settings": "Theme Settings",
                "theme": "Theme:",
                "apply_theme": "Apply Theme",
                "language": "Language:",
                "available_apps": "🎯 AVAILABLE APPLICATIONS",
                "open_all": "🚀 OPEN ALL APPS",
                "exit_suite": "❌ EXIT SUITE",
                "ready": "🟢 READY",
                "launch": "🚀 LAUNCH"
            },
            "Tiếng Việt": {
                "title": "🚀 BỘ CÔNG CỤ TÀI CHÍNH THÔNG MINH TiT 🚀",
                "subtitle": "💎 Nền tảng Phân tích Thị trường & Trí tuệ Nhân tạo 💎",
                "api_settings": "Cài đặt API",
                "google_api_key": "Khóa API Google:",
                "save_settings": "Lưu Cài đặt",
                "theme_settings": "Cài đặt Giao diện",
                "theme": "Giao diện:",
                "apply_theme": "Áp dụng Giao diện",
                "language": "Ngôn ngữ:",
                "available_apps": "🎯 CÁC ỨNG DỤNG CÓ SẴN",
                "open_all": "🚀 MỞ TẤT CẢ ỨNG DỤNG",
                "exit_suite": "❌ THOÁT CHƯƠNG TRÌNH",
                "ready": "🟢 SẴN SÀNG",
                "launch": "🚀 KHỞI CHẠY"
            }
        }

    def get_text(self, key):
        """Get translated text"""
        return self.translations[self.current_language].get(key, key)

    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def setup_styling(self):
        """Setup modern styling"""
        style = ttk.Style()
        style.theme_use('clam')

    def create_main_ui(self):
        """Create the main UI"""
        # Main container
        main_frame = tk.Frame(self.root, bg='#FFFFFF', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Header
        self.create_header(main_frame)

        # API Settings OUTSIDE menu as requested
        self.create_api_settings(main_frame)

        # Apps grid - ALL 7 APPS VISIBLE
        self.create_apps_grid(main_frame)

        # Control buttons
        self.create_controls(main_frame)

    def create_header(self, parent):
        """Create header section"""
        header_frame = tk.Frame(parent, bg='#FFFFFF')
        header_frame.pack(fill=tk.X, pady=(0, 20))

        # Title
        self.title_label = tk.Label(
            header_frame,
            text=self.get_text("title"),
            font=("Segoe UI", 20, "bold"),
            fg='#1E293B',
            bg='#FFFFFF'
        )
        self.title_label.pack(pady=(10, 5))

        # Subtitle
        self.subtitle_label = tk.Label(
            header_frame,
            text=self.get_text("subtitle"),
            font=("Segoe UI", 14, "normal"),
            fg='#3B82F6',
            bg='#FFFFFF'
        )
        self.subtitle_label.pack(pady=(0, 5))

        # Author
        author_label = tk.Label(
            header_frame,
            text="👨‍💻 NGUYEN LE VINH QUANG",
            font=("Segoe UI", 12, "bold"),
            fg='#DC2626',
            bg='#FFFFFF'
        )
        author_label.pack(pady=(5, 10))

    def create_api_settings(self, parent):
        """Create API settings OUTSIDE menu as requested"""
        settings_container = tk.Frame(parent, bg='#F8FAFC')
        settings_container.pack(fill=tk.X, pady=(0, 20))

        # API Settings Frame
        api_frame = tk.LabelFrame(
            settings_container,
            text=self.get_text("api_settings"),
            font=("Segoe UI", 10, "bold"),
            fg='#1976D2',
            bg='#FFFFFF'
        )
        api_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        # Google API key
        tk.Label(
            api_frame,
            text=self.get_text("google_api_key"),
            font=("Segoe UI", 9, "normal"),
            bg='#FFFFFF'
        ).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)

        self.google_api_entry = tk.Entry(api_frame, width=40, font=("Segoe UI", 9))
        self.google_api_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        self.google_api_entry.insert(0, "AIzaSyAlfuzPBLBLTI_j3Fpfz7iihKAmQT7u8Og")

        # Save button
        save_btn = tk.Button(
            api_frame,
            text=self.get_text("save_settings"),
            font=("Segoe UI", 9, "bold"),
            bg='#10B981',
            fg='white',
            relief=tk.FLAT,
            padx=15,
            pady=5,
            cursor="hand2",
            command=self.save_api_settings
        )
        save_btn.grid(row=1, column=1, sticky=tk.E, padx=5, pady=5)

        # Theme Settings Frame
        theme_frame = tk.LabelFrame(
            settings_container,
            text=self.get_text("theme_settings"),
            font=("Segoe UI", 10, "bold"),
            fg='#1976D2',
            bg='#FFFFFF'
        )
        theme_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        # Theme selector
        tk.Label(
            theme_frame,
            text=self.get_text("theme"),
            font=("Segoe UI", 9, "normal"),
            bg='#FFFFFF'
        ).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)

        self.theme_selector = ttk.Combobox(
            theme_frame,
            values=["clam", "alt", "default", "classic"],
            width=15,
            font=("Segoe UI", 9)
        )
        self.theme_selector.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        self.theme_selector.current(0)

        # Apply theme button
        apply_theme_btn = tk.Button(
            theme_frame,
            text=self.get_text("apply_theme"),
            font=("Segoe UI", 9, "bold"),
            bg='#3B82F6',
            fg='white',
            relief=tk.FLAT,
            padx=15,
            pady=5,
            cursor="hand2",
            command=self.apply_theme
        )
        apply_theme_btn.grid(row=1, column=1, sticky=tk.E, padx=5, pady=5)

        # Language Frame
        language_frame = tk.LabelFrame(
            settings_container,
            text=self.get_text("language"),
            font=("Segoe UI", 10, "bold"),
            fg='#1976D2',
            bg='#FFFFFF'
        )
        language_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        self.language_var = tk.StringVar(value="English")
        language_combo = ttk.Combobox(
            language_frame,
            textvariable=self.language_var,
            values=["English", "Tiếng Việt"],
            state="readonly",
            font=("Segoe UI", 9),
            width=15
        )
        language_combo.pack(padx=10, pady=10)
        language_combo.bind("<<ComboboxSelected>>", self.change_language)

    def create_apps_grid(self, parent):
        """Create apps grid with ALL 7 APPS VISIBLE"""
        apps_container = tk.LabelFrame(
            parent,
            text=self.get_text("available_apps"),
            font=("Segoe UI", 14, "bold"),
            fg='#1E293B',
            bg='#FFFFFF'
        )
        apps_container.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # Create scrollable frame
        canvas = tk.Canvas(apps_container, bg='#FFFFFF', highlightthickness=0)
        scrollbar = ttk.Scrollbar(apps_container, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='#FFFFFF')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y")

        # Configure grid for 3 columns
        for i in range(3):
            scrollable_frame.columnconfigure(i, weight=1, minsize=350)

        # Define ALL 7 applications
        self.applications = [
            {
                'name': 'TiT Crypto App',
                'description': 'Advanced Cryptocurrency Intelligence Suite',
                'file': 'Teu 1.0.1 MAin dancer.py',
                'icon': '₿',
                'color': '#F7931A'
            },
            {
                'name': 'TiT Stock App',
                'description': 'Global Stock Market Intelligence Suite',
                'file': 'TiT_Stock_App_1.0.1.py',
                'icon': '📈',
                'color': '#2E8B57'
            },
            {
                'name': 'TiT Oil App',
                'description': 'Oil Market Intelligence Suite',
                'file': 'TiT_Oil_App_1.0.1.py',
                'icon': '🛢️',
                'color': '#8B4513'
            },
            {
                'name': 'TiT Gold App',
                'description': 'Precious Metals Intelligence Suite',
                'file': 'TiT_Gold_App_1.0.1.py',
                'icon': '🥇',
                'color': '#FFD700'
            },
            {
                'name': 'TiT Health App',
                'description': 'Health & Biotech Intelligence Suite',
                'file': 'TiT_Health_App_1.0.1.py',
                'icon': '🧬',
                'color': '#2E8B57'
            },
            {
                'name': 'TiT Defense App',
                'description': 'Geopolitical & Defense Intelligence Suite',
                'file': 'TiT_Defense_App_1.0.1.py',
                'icon': '⚔️',
                'color': '#2F4F4F'
            },
            {
                'name': 'TiT Science App',
                'description': 'Science, Technology & Space Intelligence Suite',
                'file': 'TiT_Science_App_1.0.1.py',
                'icon': '🚀',
                'color': '#4169E1'
            }
        ]

        # Create app cards in grid
        row, col = 0, 0
        for app in self.applications:
            self.create_app_card(scrollable_frame, app, row, col)
            col += 1
            if col >= 3:
                col = 0
                row += 1

    def create_app_card(self, parent, app, row, col):
        """Create individual app card"""
        # Card frame
        card_frame = tk.LabelFrame(
            parent,
            text=f"  {app['icon']} {app['name']}  ",
            font=("Segoe UI", 11, "bold"),
            fg='#1E293B',
            bg='#FFFFFF',
            relief=tk.RIDGE,
            bd=2
        )
        card_frame.grid(row=row, column=col, padx=10, pady=10, sticky="nsew", ipadx=8, ipady=8)

        # Status indicator
        status_frame = tk.Frame(card_frame, bg='#FFFFFF')
        status_frame.pack(fill=tk.X, padx=8, pady=(5, 0))

        status_label = tk.Label(
            status_frame,
            text=self.get_text("ready"),
            font=("Segoe UI", 9, "bold"),
            fg='#10B981',
            bg='#FFFFFF'
        )
        status_label.pack(side=tk.RIGHT)

        # Large icon
        icon_label = tk.Label(
            card_frame,
            text=app['icon'],
            font=("Segoe UI", 32),
            bg='#FFFFFF'
        )
        icon_label.pack(pady=(8, 5))

        # Description
        desc_label = tk.Label(
            card_frame,
            text=app['description'],
            font=("Segoe UI", 9, "italic"),
            wraplength=280,
            fg='#64748B',
            bg='#FFFFFF',
            justify=tk.CENTER
        )
        desc_label.pack(fill=tk.X, padx=8, pady=(0, 8))

        # Launch button
        launch_btn = tk.Button(
            card_frame,
            text=self.get_text("launch"),
            font=("Segoe UI", 10, "bold"),
            bg='#3B82F6',
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=8,
            cursor="hand2",
            command=lambda a=app: self.launch_application(a)
        )
        launch_btn.pack(fill=tk.X, padx=8, pady=(0, 8))

        # File info
        file_label = tk.Label(
            card_frame,
            text=f"📁 {app['file']}",
            font=("Segoe UI", 8),
            fg='#94A3B8',
            bg='#FFFFFF'
        )
        file_label.pack(pady=(0, 5))

    def create_controls(self, parent):
        """Create control buttons"""
        controls_frame = tk.Frame(parent, bg='#FFFFFF')
        controls_frame.pack(fill=tk.X, pady=(0, 10))

        # Open All button
        open_all_btn = tk.Button(
            controls_frame,
            text=self.get_text("open_all"),
            font=("Segoe UI", 12, "bold"),
            bg='#10B981',
            fg='white',
            relief=tk.FLAT,
            padx=30,
            pady=12,
            cursor="hand2",
            command=self.launch_all_applications
        )
        open_all_btn.pack(side=tk.LEFT, padx=(0, 15))

        # Exit button
        exit_btn = tk.Button(
            controls_frame,
            text=self.get_text("exit_suite"),
            font=("Segoe UI", 12, "bold"),
            bg='#DC2626',
            fg='white',
            relief=tk.FLAT,
            padx=30,
            pady=12,
            cursor="hand2",
            command=self.root.quit
        )
        exit_btn.pack(side=tk.RIGHT)

        # Copyright
        copyright_label = tk.Label(
            controls_frame,
            text="© 2025 Nguyen Le Vinh Quang. All rights reserved.",
            font=("Segoe UI", 9, "italic"),
            fg='#94A3B8',
            bg='#FFFFFF'
        )
        copyright_label.pack(expand=True)

    def save_api_settings(self):
        """Save API settings to all app files"""
        try:
            google_api_key = self.google_api_entry.get().strip()

            if not google_api_key:
                messagebox.showwarning("Warning", "Please enter a Google API key")
                return

            # List of app files to update
            app_files = [
                'Teu 1.0.1 MAin dancer.py',
                'TiT_Stock_App_1.0.1.py',
                'TiT_Oil_App_1.0.1.py',
                'TiT_Gold_App_1.0.1.py',
                'TiT_Health_App_1.0.1.py',
                'TiT_Defense_App_1.0.1.py',
                'TiT_Science_App_1.0.1.py'
            ]

            updated_files = []
            for app_file in app_files:
                if os.path.exists(app_file):
                    try:
                        with open(app_file, 'r', encoding='utf-8') as f:
                            content = f.read()

                        # Update Google API key
                        content = re.sub(
                            r'GOOGLE_API_KEY\s*=\s*["\'][^"\']*["\']',
                            f'GOOGLE_API_KEY = "{google_api_key}"',
                            content
                        )

                        with open(app_file, 'w', encoding='utf-8') as f:
                            f.write(content)

                        updated_files.append(app_file)
                    except Exception as e:
                        print(f"Error updating {app_file}: {e}")

            if updated_files:
                messagebox.showinfo(
                    "Success",
                    f"API settings saved to {len(updated_files)} apps:\n" +
                    "\n".join(updated_files)
                )
            else:
                messagebox.showwarning("Warning", "No app files found to update")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save API settings: {str(e)}")

    def apply_theme(self):
        """Apply selected theme"""
        try:
            selected_theme = self.theme_selector.get()
            style = ttk.Style()
            style.theme_use(selected_theme)
            messagebox.showinfo("Success", f"Theme '{selected_theme}' applied successfully!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to apply theme: {str(e)}")

    def change_language(self, event=None):
        """Change language"""
        try:
            self.current_language = self.language_var.get()

            # Update UI text
            self.title_label.config(text=self.get_text("title"))
            self.subtitle_label.config(text=self.get_text("subtitle"))

            messagebox.showinfo("Success", f"Language changed to {self.current_language}")

            # Recreate UI to apply language changes
            for widget in self.root.winfo_children():
                widget.destroy()
            self.create_main_ui()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to change language: {str(e)}")

    def launch_application(self, app):
        """Launch a specific application"""
        try:
            app_file = app['file']

            if not os.path.exists(app_file):
                messagebox.showerror(
                    "File Not Found",
                    f"Application file '{app_file}' not found.\n\n"
                    f"Please ensure all TiT apps are in the same directory."
                )
                return

            # Launch the app
            process = subprocess.Popen([sys.executable, app_file])

            messagebox.showinfo(
                "Launch Successful!",
                f"✅ {app['name']} is starting!\n\n"
                f"⚡ Application window will appear shortly\n"
                f"🎯 Process ID: {process.pid}"
            )

        except Exception as e:
            messagebox.showerror("Launch Error", f"Failed to launch {app['name']}:\n\n{str(e)}")

    def launch_all_applications(self):
        """Launch all applications"""
        try:
            result = messagebox.askyesno(
                "Launch All Applications",
                "This will launch all 7 TiT applications.\n\nContinue?"
            )

            if not result:
                return

            launched = 0
            failed = []

            for app in self.applications:
                try:
                    if os.path.exists(app['file']):
                        subprocess.Popen([sys.executable, app['file']])
                        launched += 1
                    else:
                        failed.append(f"{app['name']} (file not found)")
                except Exception as e:
                    failed.append(f"{app['name']} (error: {str(e)})")

            message = f"✅ Successfully launched {launched} applications!"
            if failed:
                message += f"\n\n❌ Failed to launch:\n" + "\n".join(failed)

            messagebox.showinfo("Launch Results", message)

        except Exception as e:
            messagebox.showerror("Launch Error", f"Failed to launch applications: {str(e)}")

def main():
    """Main function"""
    print("🚀 Starting TiT Suite Launcher...")
    print("💎 ALL 7 APPS VISIBLE with API SETTINGS OUTSIDE MENU")
    print("👨‍💻 Created by Nguyen Le Vinh Quang")

    root = tk.Tk()
    launcher = TiTSuiteLauncher(root)

    print("✅ TiT Suite launcher ready!")
    root.mainloop()

if __name__ == "__main__":
    main()
