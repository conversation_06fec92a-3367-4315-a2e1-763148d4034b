#!/usr/bin/env python3
"""
START TiT SUITE - Quick Launcher
Launches all 7 TiT applications with beautiful interface
Created by <PERSON><PERSON><PERSON>
"""

import tkinter as tk
from tkinter import messagebox, ttk
import subprocess
import sys
import os
import threading
from datetime import datetime

class TiTSuiteLauncher:
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.create_interface()
        
    def setup_window(self):
        """Setup main window"""
        self.root.title("🚀 TiT Suite 1.0.1 - Quick Launcher")
        self.root.geometry("800x600")
        self.root.configure(bg='#2C3E50')
        
        # Center window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (800 // 2)
        y = (self.root.winfo_screenheight() // 2) - (600 // 2)
        self.root.geometry(f"800x600+{x}+{y}")
        
    def create_interface(self):
        """Create launcher interface"""
        # Header
        header_frame = tk.Frame(self.root, bg='#2C3E50')
        header_frame.pack(fill=tk.X, pady=20)
        
        title_label = tk.Label(
            header_frame,
            text="🚀 TiT Suite 1.0.1",
            font=("Arial", 24, "bold"),
            fg='#ECF0F1',
            bg='#2C3E50'
        )
        title_label.pack()
        
        subtitle_label = tk.Label(
            header_frame,
            text="Professional Trading Intelligence Suite",
            font=("Arial", 12),
            fg='#BDC3C7',
            bg='#2C3E50'
        )
        subtitle_label.pack(pady=(5, 0))
        
        # Apps list
        self.apps = [
            {'name': 'TiT Crypto App', 'file': 'Teu 1.0.1 MAin dancer.py', 'icon': '₿'},
            {'name': 'TiT Stock App', 'file': 'TiT_Apps/TiT_Stock_App_1.0.1.py', 'icon': '📈'},
            {'name': 'TiT Oil App', 'file': 'TiT_Apps/TiT_Oil_App_1.0.1.py', 'icon': '🛢️'},
            {'name': 'TiT Gold App', 'file': 'TiT_Apps/TiT_Gold_App_1.0.1.py', 'icon': '🥇'},
            {'name': 'TiT Health App', 'file': 'TiT_Apps/TiT_Health_App_1.0.1.py', 'icon': '🏥'},
            {'name': 'TiT Defense App', 'file': 'TiT_Apps/TiT_Defense_App_1.0.1.py', 'icon': '🛡️'},
            {'name': 'TiT Science App', 'file': 'TiT_Apps/TiT_Science_App_1.0.1.py', 'icon': '🚀'}
        ]
        
        # Apps frame
        apps_frame = tk.Frame(self.root, bg='#2C3E50')
        apps_frame.pack(fill=tk.BOTH, expand=True, padx=40, pady=20)
        
        for i, app in enumerate(self.apps):
            self.create_app_button(apps_frame, app, i)
        
        # Control buttons
        control_frame = tk.Frame(self.root, bg='#2C3E50')
        control_frame.pack(fill=tk.X, pady=20)
        
        launch_all_btn = tk.Button(
            control_frame,
            text="🚀 Launch All Apps",
            command=self.launch_all,
            font=("Arial", 14, "bold"),
            bg='#27AE60',
            fg='white',
            relief='flat',
            padx=20,
            pady=10
        )
        launch_all_btn.pack(side=tk.LEFT, padx=20)
        
        exit_btn = tk.Button(
            control_frame,
            text="❌ Exit",
            command=self.root.quit,
            font=("Arial", 14, "bold"),
            bg='#E74C3C',
            fg='white',
            relief='flat',
            padx=20,
            pady=10
        )
        exit_btn.pack(side=tk.RIGHT, padx=20)
        
    def create_app_button(self, parent, app, index):
        """Create individual app launch button"""
        btn_frame = tk.Frame(parent, bg='#34495E', relief='raised', bd=2)
        btn_frame.pack(fill=tk.X, pady=5)
        
        # Check if file exists
        file_exists = os.path.exists(app['file'])
        status_color = '#27AE60' if file_exists else '#E74C3C'
        status_text = 'Ready' if file_exists else 'Missing'
        
        btn = tk.Button(
            btn_frame,
            text=f"{app['icon']} {app['name']} - {status_text}",
            command=lambda a=app: self.launch_app(a),
            font=("Arial", 12, "bold"),
            bg='#34495E',
            fg=status_color,
            relief='flat',
            anchor='w',
            padx=20,
            pady=10,
            state='normal' if file_exists else 'disabled'
        )
        btn.pack(fill=tk.X)
        
    def launch_app(self, app):
        """Launch individual app"""
        try:
            if not os.path.exists(app['file']):
                messagebox.showerror("Error", f"File not found: {app['file']}")
                return
                
            subprocess.Popen([sys.executable, app['file']])
            messagebox.showinfo("Success", f"{app['name']} launched successfully!")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch {app['name']}: {str(e)}")
    
    def launch_all(self):
        """Launch all available apps"""
        try:
            launched = 0
            failed = []
            
            for app in self.apps:
                try:
                    if os.path.exists(app['file']):
                        subprocess.Popen([sys.executable, app['file']])
                        launched += 1
                    else:
                        failed.append(app['name'])
                except Exception as e:
                    failed.append(f"{app['name']} (Error: {str(e)})")
            
            message = f"Successfully launched {launched} applications!"
            if failed:
                message += f"\n\nFailed to launch:\n" + "\n".join(failed)
            
            messagebox.showinfo("Launch Results", message)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch applications: {str(e)}")

def main():
    """Main function"""
    print("🚀 Starting TiT Suite Launcher...")
    
    root = tk.Tk()
    launcher = TiTSuiteLauncher(root)
    
    print("✅ Launcher ready!")
    root.mainloop()

if __name__ == "__main__":
    main()
