# START TiT SUITE - <PERSON>IN LAUNCHER
# Version: 1.0.1 (COMPLETE RESTORATION)
#
# Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
# Author: <PERSON><PERSON><PERSON>
# Date: 2025-06-20
#
# RESTORED LAUNCHER WITH ALL 7 APPS VISIBLE AND API SETTINGS OUTSIDE MENU

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import subprocess
import sys
import os
import re

class TiTSuiteLauncher:
    def __init__(self, root):
        self.root = root
        self.root.title("🚀 TiT Financial Intelligence Suite 1.0.1 - Complete Launcher 🚀")
        self.root.geometry("1400x900")
        self.root.configure(bg='#F8FAFC')
        self.root.resizable(True, True)
        self.root.minsize(1200, 800)

        # Initialize language system
        self.current_language = "English"
        self.setup_translations()

        # Center window
        self.center_window()

        # Configure styling
        self.setup_styling()

        # Create main UI
        self.create_main_ui()

    def setup_translations(self):
        """Setup Vietnamese translations"""
        self.translations = {
            "English": {
                "title": "🚀 TiT FINANCIAL INTELLIGENCE SUITE 🚀",
                "subtitle": "💎 Market Intelligence & AI Analysis Platform 💎",
                "api_settings": "API Settings",
                "google_api_key": "Google API Key:",
                "save_settings": "Save Settings",
                "theme_settings": "Theme Settings",
                "theme": "Theme:",
                "apply_theme": "Apply Theme",
                "language": "Language:",
                "available_apps": "🎯 AVAILABLE APPLICATIONS",
                "open_all": "🚀 OPEN ALL APPS",
                "exit_suite": "❌ EXIT SUITE",
                "ready": "🟢 READY",
                "launch": "🚀 LAUNCH"
            },
            "Tiếng Việt": {
                "title": "🚀 BỘ CÔNG CỤ TÀI CHÍNH THÔNG MINH TiT 🚀",
                "subtitle": "💎 Nền tảng Phân tích Thị trường & Trí tuệ Nhân tạo 💎",
                "api_settings": "Cài đặt API",
                "google_api_key": "Khóa API Google:",
                "save_settings": "Lưu Cài đặt",
                "theme_settings": "Cài đặt Giao diện",
                "theme": "Giao diện:",
                "apply_theme": "Áp dụng Giao diện",
                "language": "Ngôn ngữ:",
                "available_apps": "🎯 CÁC ỨNG DỤNG CÓ SẴN",
                "open_all": "🚀 MỞ TẤT CẢ ỨNG DỤNG",
                "exit_suite": "❌ THOÁT CHƯƠNG TRÌNH",
                "ready": "🟢 SẴN SÀNG",
                "launch": "🚀 KHỞI CHẠY"
            }
        }

    def get_text(self, key):
        """Get translated text"""
        return self.translations[self.current_language].get(key, key)

    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def setup_styling(self):
        """Setup modern styling"""
        style = ttk.Style()
        style.theme_use('clam')

    def create_main_ui(self):
        """Create the main UI"""
        # Main container
        main_frame = tk.Frame(self.root, bg='#FFFFFF', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Header
        self.create_header(main_frame)

        # API Settings OUTSIDE menu as requested
        self.create_api_settings(main_frame)

        # Apps grid - ALL 7 APPS VISIBLE
        self.create_apps_grid(main_frame)

        # Control buttons
        self.create_controls(main_frame)

    def create_header(self, parent):
        """Create header section"""
        header_frame = tk.Frame(parent, bg='#FFFFFF')
        header_frame.pack(fill=tk.X, pady=(0, 20))

        # Title
        self.title_label = tk.Label(
            header_frame,
            text=self.get_text("title"),
            font=("Segoe UI", 20, "bold"),
            fg='#1E293B',
            bg='#FFFFFF'
        )
        self.title_label.pack(pady=(10, 5))

        # Subtitle
        self.subtitle_label = tk.Label(
            header_frame,
            text=self.get_text("subtitle"),
            font=("Segoe UI", 14, "normal"),
            fg='#3B82F6',
            bg='#FFFFFF'
        )
        self.subtitle_label.pack(pady=(0, 5))

        # Author
        author_label = tk.Label(
            header_frame,
            text="👨‍💻 NGUYEN LE VINH QUANG",
            font=("Segoe UI", 12, "bold"),
            fg='#DC2626',
            bg='#FFFFFF'
        )
        author_label.pack(pady=(5, 10))

    def create_api_settings(self, parent):
        """Create API settings OUTSIDE menu as requested"""
        settings_container = tk.Frame(parent, bg='#F8FAFC')
        settings_container.pack(fill=tk.X, pady=(0, 20))

        # API Settings Frame
        api_frame = tk.LabelFrame(
            settings_container,
            text=self.get_text("api_settings"),
            font=("Segoe UI", 10, "bold"),
            fg='#1976D2',
            bg='#FFFFFF'
        )
        api_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        # Google API key
        tk.Label(
            api_frame,
            text=self.get_text("google_api_key"),
            font=("Segoe UI", 9, "normal"),
            bg='#FFFFFF'
        ).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)

        self.google_api_entry = tk.Entry(api_frame, width=40, font=("Segoe UI", 9))
        self.google_api_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        self.google_api_entry.insert(0, "AIzaSyAlfuzPBLBLTI_j3Fpfz7iihKAmQT7u8Og")

        # Save button
        save_btn = tk.Button(
            api_frame,
            text=self.get_text("save_settings"),
            font=("Segoe UI", 9, "bold"),
            bg='#10B981',
            fg='white',
            relief=tk.FLAT,
            padx=15,
            pady=5,
            cursor="hand2",
            command=self.save_api_settings
        )
        save_btn.grid(row=1, column=1, sticky=tk.E, padx=5, pady=5)

        # Theme Settings Frame
        theme_frame = tk.LabelFrame(
            settings_container,
            text=self.get_text("theme_settings"),
            font=("Segoe UI", 10, "bold"),
            fg='#1976D2',
            bg='#FFFFFF'
        )
        theme_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        # Theme selector
        tk.Label(
            theme_frame,
            text=self.get_text("theme"),
            font=("Segoe UI", 9, "normal"),
            bg='#FFFFFF'
        ).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)

        self.theme_selector = ttk.Combobox(
            theme_frame,
            values=["clam", "alt", "default", "classic"],
            width=15,
            font=("Segoe UI", 9)
        )
        self.theme_selector.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        self.theme_selector.current(0)

        # Apply theme button
        apply_theme_btn = tk.Button(
            theme_frame,
            text=self.get_text("apply_theme"),
            font=("Segoe UI", 9, "bold"),
            bg='#3B82F6',
            fg='white',
            relief=tk.FLAT,
            padx=15,
            pady=5,
            cursor="hand2",
            command=self.apply_theme
        )
        apply_theme_btn.grid(row=1, column=1, sticky=tk.E, padx=5, pady=5)

        # Language Frame
        language_frame = tk.LabelFrame(
            settings_container,
            text=self.get_text("language"),
            font=("Segoe UI", 10, "bold"),
            fg='#1976D2',
            bg='#FFFFFF'
        )
        language_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        self.language_var = tk.StringVar(value="English")
        language_combo = ttk.Combobox(
            language_frame,
            textvariable=self.language_var,
            values=["English", "Tiếng Việt"],
            state="readonly",
            font=("Segoe UI", 9),
            width=15
        )
        language_combo.pack(padx=10, pady=10)
        language_combo.bind("<<ComboboxSelected>>", self.change_language)
