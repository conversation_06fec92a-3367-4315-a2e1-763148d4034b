# Data Optimization Module for TiT Suite
# Ultra-fast data loading and optimization engines
# Copyright (C) 2025 <PERSON><PERSON><PERSON>. All rights reserved.

import asyncio
import threading
import time
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import requests
from typing import Dict, List, Any, Optional
import json

class UltraFastLoader:
    """Ultra-fast data loading with advanced optimization engines"""
    
    def __init__(self):
        self.cache = {}
        self.cache_timestamps = {}
        self.cache_duration = 300  # 5 minutes
        self.thread_pool = ThreadPoolExecutor(max_workers=10)
        self.rate_limiter = RateLimiter()
        logging.info("🚀 UltraFastLoader initialized with 10 worker threads")
    
    def ultra_fast_load(self, urls: List[str], timeout: float = 5.0) -> Dict[str, Any]:
        """Load multiple URLs with ultra-fast parallel processing"""
        results = {}
        
        # Check cache first
        cached_results = {}
        urls_to_fetch = []
        
        for url in urls:
            if self._is_cached(url):
                cached_results[url] = self.cache[url]
                logging.info(f"⚡ Cache hit for {url[:50]}...")
            else:
                urls_to_fetch.append(url)
        
        # Fetch uncached URLs in parallel
        if urls_to_fetch:
            futures = {}
            for url in urls_to_fetch:
                future = self.thread_pool.submit(self._fetch_with_optimization, url, timeout)
                futures[future] = url
            
            for future in as_completed(futures, timeout=timeout * 2):
                url = futures[future]
                try:
                    result = future.result()
                    results[url] = result
                    self._cache_result(url, result)
                except Exception as e:
                    logging.error(f"❌ Failed to fetch {url}: {e}")
                    results[url] = None
        
        # Combine cached and new results
        results.update(cached_results)
        return results
    
    def _fetch_with_optimization(self, url: str, timeout: float) -> Optional[Dict]:
        """Fetch single URL with rate limiting and optimization"""
        try:
            # Rate limiting
            self.rate_limiter.wait_if_needed()
            
            # Optimized headers
            headers = {
                'User-Agent': 'TiT-Suite/1.0.1 (Ultra-Fast-Loader)',
                'Accept': 'application/json,text/html,application/xhtml+xml',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Cache-Control': 'no-cache'
            }
            
            response = requests.get(url, headers=headers, timeout=timeout)
            response.raise_for_status()
            
            # Try to parse as JSON, fallback to text
            try:
                return response.json()
            except:
                return {'text': response.text, 'status_code': response.status_code}
                
        except Exception as e:
            logging.error(f"❌ Fetch error for {url}: {e}")
            return None
    
    def _is_cached(self, url: str) -> bool:
        """Check if URL result is cached and still valid"""
        if url not in self.cache:
            return False
        
        timestamp = self.cache_timestamps.get(url, 0)
        return (time.time() - timestamp) < self.cache_duration
    
    def _cache_result(self, url: str, result: Any):
        """Cache the result with timestamp"""
        self.cache[url] = result
        self.cache_timestamps[url] = time.time()
    
    def clear_cache(self):
        """Clear all cached data"""
        self.cache.clear()
        self.cache_timestamps.clear()
        logging.info("🧹 Cache cleared")

class RateLimiter:
    """Rate limiter to prevent API overload"""
    
    def __init__(self, max_requests_per_second: float = 10.0):
        self.max_requests_per_second = max_requests_per_second
        self.min_interval = 1.0 / max_requests_per_second
        self.last_request_time = 0
        self.lock = threading.Lock()
    
    def wait_if_needed(self):
        """Wait if necessary to respect rate limits"""
        with self.lock:
            current_time = time.time()
            time_since_last = current_time - self.last_request_time
            
            if time_since_last < self.min_interval:
                sleep_time = self.min_interval - time_since_last
                time.sleep(sleep_time)
            
            self.last_request_time = time.time()

class DataOptimizer:
    """Advanced data optimization and processing"""
    
    @staticmethod
    def optimize_json_data(data: Dict) -> Dict:
        """Optimize JSON data structure for faster processing"""
        if not isinstance(data, dict):
            return data
        
        optimized = {}
        for key, value in data.items():
            # Convert string numbers to actual numbers
            if isinstance(value, str) and value.replace('.', '').replace('-', '').isdigit():
                try:
                    optimized[key] = float(value) if '.' in value else int(value)
                except:
                    optimized[key] = value
            elif isinstance(value, dict):
                optimized[key] = DataOptimizer.optimize_json_data(value)
            elif isinstance(value, list):
                optimized[key] = [DataOptimizer.optimize_json_data(item) if isinstance(item, dict) else item for item in value]
            else:
                optimized[key] = value
        
        return optimized
    
    @staticmethod
    def compress_data(data: Any) -> bytes:
        """Compress data for efficient storage"""
        import gzip
        import pickle
        
        try:
            serialized = pickle.dumps(data)
            compressed = gzip.compress(serialized)
            return compressed
        except Exception as e:
            logging.error(f"❌ Compression failed: {e}")
            return b''
    
    @staticmethod
    def decompress_data(compressed_data: bytes) -> Any:
        """Decompress data"""
        import gzip
        import pickle
        
        try:
            decompressed = gzip.decompress(compressed_data)
            data = pickle.loads(decompressed)
            return data
        except Exception as e:
            logging.error(f"❌ Decompression failed: {e}")
            return None

# Global instances
ultra_fast_loader = UltraFastLoader()
data_optimizer = DataOptimizer()

# Convenience functions
def fast_load_multiple(urls: List[str], timeout: float = 5.0) -> Dict[str, Any]:
    """Load multiple URLs with ultra-fast optimization"""
    return ultra_fast_loader.ultra_fast_load(urls, timeout)

def optimize_data(data: Dict) -> Dict:
    """Optimize data structure"""
    return data_optimizer.optimize_json_data(data)

def clear_optimization_cache():
    """Clear optimization cache"""
    ultra_fast_loader.clear_cache()

logging.info("✅ Data optimization module loaded successfully!")
